# Environment Configuration Template for PharmaGuide
# Copy this file to .env and fill in your actual values
# DO NOT commit .env to version control

# Supabase Configuration - REQUIRED
EXPO_PUBLIC_SUPABASE_URL=your_supabase_project_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# Product Database
EXPO_PUBLIC_OPENFOODFACTS_API=https://world.openfoodfacts.org

# App Configuration
EXPO_PUBLIC_APP_ENV=development
EXPO_PUBLIC_API_BASE_URL=your_supabase_project_url
EXPO_PUBLIC_ENVIRONMENT=development
EXPO_PUBLIC_APP_NAME=PharmaGuide
EXPO_PUBLIC_APP_VERSION=1.0.0

# AI Service Configuration (Optional - recommended to use Supabase Edge Functions)
# EXPO_PUBLIC_HUGGINGFACE_API_KEY=your_huggingface_api_key
# EXPO_PUBLIC_GROQ_API_KEY=your_groq_api_key