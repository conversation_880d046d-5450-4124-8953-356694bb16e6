# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/

# Expo/React Native
.expo/
.expo-shared/
dist/
build/
web-build/
expo-env.d.ts

# Build output (native, if ejected)
android/
ios/

# Environment variables
.env
.env.*
.env.local
.env*.local
!.env.example
.env.keys

# Native
.kotlin/
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*
pnpm-debug.log*

# macOS
.DS_Store
*.pem

# typescript
*.tsbuildinfo

# TestHuggingFace
testHuggingFace.js

# IDE files
.vscode/
.idea/
*.sublime-workspace
*.sublime-project

# Test/coverage output
coverage/

# Misc
*.log
*.lock
*.tmp
*.swp

# VSCode settings (optional, already above)
# .vscode/
# @generated expo-cli sync-8d4afeec25ea8a192358fae2f8e2fc766bdce4ec
# The following patterns were generated by expo-cli

# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/

# Expo
.expo/
dist/
web-build/
expo-env.d.ts

# Native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
.env*.local

# typescript
*.tsbuildinfo

# @end expo-cli