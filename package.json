{"name": "pharmaguide", "version": "1.0.0", "main": "expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "supabase:start": "npx supabase start", "supabase:stop": "npx supabase stop", "supabase:functions:serve": "npx supabase functions serve ai-analysis", "lint": "eslint . --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint . --ext .ts,.tsx,.js,.jsx --fix", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md}\"", "type-check": "tsc --noEmit", "test": "jest", "test:unit": "jest --testPathPattern=src/.*\\.(test|spec)\\.(ts|tsx|js)$ --testPathIgnorePatterns=src/tests/e2e/", "test:integration": "jest --testPathPattern=src/tests/integration/", "test:e2e": "detox test", "test:e2e:build": "detox build", "test:maestro": "maestro test maestro/flows/", "test:maestro:auth": "maestro test maestro/flows/authentication.yaml", "test:maestro:scan": "maestro test maestro/flows/product-scanning.yaml", "test:flashlight": "flashlight test --config flashlight/test-scenarios.yml", "test:performance": "jest --testPathPattern=src/tests/performance/", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false --passWithNoTests", "test:all": "npm run code-quality && npm run test:unit && npm run test:integration", "lint:check": "eslint . --ext .ts,.tsx,.js,.jsx --format json --output-file eslint-report.json", "code-quality": "npm run type-check && npm run lint && npm run format:check", "quality-gate": "node scripts/quality-gate.js", "generate-quality-report": "node scripts/generate-quality-report.js", "bundle-analyzer": "ANALYZE_BUNDLE=true npx expo export --platform web && npx webpack-bundle-analyzer web-build/static/js/*.js", "bundle-stats": "npx expo export --platform web --analyze && node scripts/bundle-stats.js", "build:production": "expo build:web", "deploy:staging": "echo 'Deploy to staging'", "deploy:production": "echo 'Deploy to production'", "prepare": "husky install || true", "pre-commit": "lint-staged", "pre-push": "npm run test:ci && npm run quality-gate"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.1.0", "@gorhom/bottom-sheet": "^5.1.6", "@huggingface/inference": "^4.0.5", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/netinfo": "^11.4.1", "@react-native-picker/picker": "2.11.0", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.10", "@react-navigation/native-stack": "^7.3.21", "@react-navigation/stack": "^7.4.2", "@reduxjs/toolkit": "^2.8.2", "@sentry/react-native": "^6.16.0", "@supabase/supabase-js": "^2.50.1", "axios": "^1.10.0", "babel-preset-expo": "^13.2.1", "crypto-js": "^4.2.0", "debounce": "^2.2.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.44.2", "expo": "~53.0.10", "expo-barcode-scanner": "^13.0.1", "expo-blur": "~14.1.5", "expo-camera": "~16.1.7", "expo-constants": "^17.1.6", "expo-crypto": "~14.1.5", "expo-device": "^7.1.4", "expo-haptics": "^14.1.4", "expo-image": "^2.3.0", "expo-linear-gradient": "~14.1.5", "expo-notifications": "^0.31.3", "expo-random": "~14.0.1", "expo-secure-store": "~14.2.3", "expo-sqlite": "~15.2.12", "expo-status-bar": "~2.2.3", "ionicons": "^8.0.9", "lodash": "^4.17.21", "pako": "^2.1.0", "react": "19.0.0", "react-error-boundary": "^6.0.0", "react-loading-skeleton": "^3.5.0", "react-native": "0.79.4", "react-native-dotenv": "^3.4.11", "react-native-gesture-handler": "~2.24.0", "react-native-mmkv": "^2.12.2", "react-native-paper": "^5.14.5", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "^4.11.1", "react-native-super-grid": "^6.0.1", "react-native-svg": "15.11.2", "react-native-toast-message": "^2.3.0", "react-native-web": "^0.20.0", "react-redux": "^9.2.0", "zustand": "^5.0.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@react-native-community/cli": "latest", "@testing-library/jest-dom": "^6.6.3", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.2.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/lodash": "^4.17.19", "@types/react": "~19.0.10", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "drizzle-kit": "^0.31.2", "eslint": "^8.57.0", "eslint-config-expo": "^9.2.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.32.0", "eslint-plugin-prettier": "^5.5.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-native": "^5.0.0", "jest": "^29.7.0", "jest-expo": "^53.0.7", "prettier": "^3.5.3", "react-test-renderer": "^19.0.0", "supabase": "^2.26.9", "ts-jest": "^29.4.0", "typescript": "~5.8.3"}, "private": true}