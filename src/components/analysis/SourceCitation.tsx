// src/components/analysis/SourceCitation.tsx
// 📚 SOURCE CITATION & EVIDENCE ATTRIBUTION
// Transparent source references for user trust

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
  Linking,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { supabase } from '../../services/supabase/client';
import { COLORS, SPACING, TYPOGRAPHY, EVIDENCE_LEVELS } from '../../constants';
import { useAccessibility } from '../../hooks/useAccessibility';
import {
  DatabaseSourceReference,
  VerificationLevel,
  EnhancedIngredientAnalysis,
} from '../../types';
import { VerificationBadge, EvidenceGrade, VerificationLevel as VBVerificationLevel } from './VerificationBadge';

interface SourceCitationProps {
  sources: DatabaseSourceReference[];
  ingredientAnalysis?: EnhancedIngredientAnalysis;
  compact?: boolean;
  maxVisible?: number;
  onSourcePress?: (source: DatabaseSourceReference) => void;
  userId?: string;
}

export const SourceCitation: React.FC<SourceCitationProps> = ({
  sources,
  ingredientAnalysis,
  compact = false,
  maxVisible = 3,
  onSourcePress,
  userId = 'local',
}) => {
  const { announceForScreenReader } = useAccessibility();
  const [showAllSources, setShowAllSources] = useState(false);
  const [selectedSource, setSelectedSource] =
    useState<DatabaseSourceReference | null>(null);
  const [dbSources, setDbSources] = useState<DatabaseSourceReference[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const fetchDbSources = async () => {
      setIsLoading(true);
      try {
        const sourceIds = [
          ...sources.map(s => s.id),
          ...(ingredientAnalysis?.dataSources?.map(ds => `ds_${ds}`) || []),
        ];
        const { data, error } = await supabase
          .from('interaction_sources')
          .select(
            'id, source_type, source_name, source_url, publication_date, study_type, confidence_score'
          )
          .in('id', sourceIds);
        if (error) throw error;
        const mappedSources: DatabaseSourceReference[] =
          data?.map(d => ({
            id: d.id,
            source: d.source_name,
            url: d.source_url,
            title: d.source_name,
            verificationLevel: mapSourceTypeToVerificationLevel(d.source_type),
            accessDate: new Date().toISOString(),
          })) || [];
        setDbSources(mappedSources);
        await supabase.from('analytics_events').insert({
          user_id: userId,
          action: 'fetch_sources',
          metadata: { sourceIds },
        });
        await supabase.from('audit_log').insert({
          table_name: 'interaction_sources',
          record_id: data?.[0]?.id || 'multiple',
          operation: 'SELECT',
          user_id: userId,
        });
      } catch (error: any) {
        console.error('Error fetching sources:', error);
        setDbSources(sources);
      } finally {
        setIsLoading(false);
      }
    };
    if (sources.length || ingredientAnalysis?.dataSources?.length)
      fetchDbSources();
  }, [sources, ingredientAnalysis, userId]);

  const visibleSources = showAllSources
    ? dbSources.length
      ? dbSources
      : sources
    : (dbSources.length ? dbSources : sources).slice(0, maxVisible);
  const hasMoreSources = sources.length > maxVisible;

  const handleSourcePress = async (source: DatabaseSourceReference) => {
    try {
      await supabase.from('analytics_events').insert({
        user_id: userId,
        action: 'view_source',
        metadata: { source_id: source.id, source_type: source.type },
      });
      if (onSourcePress) {
        onSourcePress(source);
      } else {
        setSelectedSource(source);
      }
    } catch (error) {
      console.error('Error logging source view:', error);
    }
  };

  const openSourceUrl = async (source: DatabaseSourceReference) => {
    const url =
      source.url ||
      (source.pmid
        ? `https://pubmed.ncbi.nlm.nih.gov/${source.pmid}/`
        : source.doi
          ? `https://doi.org/${source.doi}`
          : '');
    if (url) {
      try {
        const supported = await Linking.canOpenURL(url);
        if (supported) {
          await Linking.openURL(url);
          await supabase.from('analytics_events').insert({
            user_id: userId,
            action: 'open_source_url',
            metadata: { source_id: source.id, url },
          });
        } else {
          Alert.alert('Error', 'Cannot open this link');
        }
      } catch (error) {
        console.error('Error opening source URL:', error);
        Alert.alert('Error', 'Cannot open this link');
      }
    }
  };

  if (compact) {
    return (
      <View style={styles.compactContainer}>
        <Ionicons name="library-outline" size={12} color={COLORS.gray500} />
        <Text style={styles.compactText}>
          {sources.length + (ingredientAnalysis?.dataSources?.length || 0)}{' '}
          source
          {sources.length + (ingredientAnalysis?.dataSources?.length || 0) !== 1
            ? 's'
            : ''}
        </Text>
        <TouchableOpacity onPress={() => setShowAllSources(true)}>
          <Text style={styles.viewAllText}>View</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Ionicons name="library" size={16} color={COLORS.primary} />
        <Text style={styles.headerText}>Evidence Sources</Text>
        <Text style={styles.countText}>
          ({sources.length + (ingredientAnalysis?.dataSources?.length || 0)})
        </Text>
      </View>

      {isLoading ? (
        <Text style={styles.loadingText}>Loading sources...</Text>
      ) : (
        visibleSources.map((source, index) => (
          <SourceItem
            key={source.id}
            source={source}
            index={index + 1}
            onPress={() => handleSourcePress(source)}
          />
        ))
      )}

      {hasMoreSources && !showAllSources && (
        <TouchableOpacity
          style={styles.showMoreButton}
          onPress={() => setShowAllSources(true)}
        >
          <Text style={styles.showMoreText}>
            Show{' '}
            {sources.length +
              (ingredientAnalysis?.dataSources?.length || 0) -
              maxVisible}{' '}
            more sources
          </Text>
          <Ionicons name="chevron-down" size={14} color={COLORS.primary} />
        </TouchableOpacity>
      )}

      <Modal
        visible={selectedSource !== null}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setSelectedSource(null)}
        testID="source-detail-modal"
      >
        {selectedSource && (
          <SourceDetailModal
            source={selectedSource}
            onClose={() => setSelectedSource(null)}
            onOpenUrl={() => openSourceUrl(selectedSource)}
          />
        )}
      </Modal>
    </View>
  );
};

interface SourceItemProps {
  source: DatabaseSourceReference;
  index: number;
  onPress: () => void;
}

const SourceItem: React.FC<SourceItemProps> = ({ source, index, onPress }) => {
  const typeConfig = getSourceTypeConfig(source.type);
  const accessibilityLabel = `Source ${index}: ${source.title}, ${typeConfig.label}, Evidence Grade ${source.evidenceLevel || 'N/A'}, ${Math.round((source.relevanceScore || 0) * 100)}% relevant`;

  return (
    <TouchableOpacity
      style={styles.sourceItem}
      onPress={onPress}
      accessibilityLabel={accessibilityLabel}
      accessibilityRole="button"
    >
      <View style={styles.sourceHeader}>
        <VerificationBadge
          level={'CLINICAL_STUDY' as VBVerificationLevel}
          evidenceGrade={(source.evidenceLevel as 'A' | 'B' | 'C' | 'D') || 'C'}
          compact
        />
        <View style={styles.sourceNumber}>
          <Text style={styles.sourceNumberText}>{index}</Text>
        </View>
      </View>

      <Text style={styles.sourceTitle} numberOfLines={2}>
        {source.title}
      </Text>

      {source.authors && source.authors.length > 0 && (
        <Text style={styles.sourceAuthors} numberOfLines={1}>
          {source.authors.slice(0, 3).join(', ')}
          {source.authors.length > 3 && ' et al.'}
        </Text>
      )}

      {source.journal && source.year && (
        <Text style={styles.sourceJournal}>
          {source.journal} ({source.year})
        </Text>
      )}

      <View style={styles.sourceFooter}>
        <View style={styles.relevanceScore}>
          <Ionicons name="star" size={12} color={COLORS.accent} />
          <Text style={styles.relevanceText}>
            {Math.round((source.relevanceScore || 0) * 100)}% relevant
          </Text>
        </View>
        {(source.url || source.pmid || source.doi) && (
          <Ionicons name="link" size={12} color={COLORS.primary} />
        )}
      </View>
    </TouchableOpacity>
  );
};

interface SourceDetailModalProps {
  source: DatabaseSourceReference;
  onClose: () => void;
  onOpenUrl: () => void;
}

const SourceDetailModal: React.FC<SourceDetailModalProps> = ({
  source,
  onClose,
  onOpenUrl,
}) => {
  const typeConfig = getSourceTypeConfig(source.type);

  return (
    <View style={styles.modalContainer}>
      <View style={styles.modalHeader}>
        <TouchableOpacity onPress={onClose}>
          <Ionicons name="close" size={24} color={COLORS.gray600} />
        </TouchableOpacity>
        <Text style={styles.modalTitle}>Source Details</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.modalContent}>
        <View style={styles.modalSourceHeader}>
          <VerificationBadge
            level={source.type ? (source.type as any) : 'AI_ANALYSIS'}
            evidenceGrade={source.evidenceLevel as EvidenceGrade}
          />
        </View>

        <Text style={styles.modalSourceTitle}>{source.title}</Text>

        {source.authors && source.authors.length > 0 && (
          <View style={styles.modalSection}>
            <Text style={styles.modalSectionTitle}>Authors</Text>
            <Text style={styles.modalSectionText}>
              {source.authors.join(', ')}
            </Text>
          </View>
        )}

        {source.journal && (
          <View style={styles.modalSection}>
            <Text style={styles.modalSectionTitle}>Publication</Text>
            <Text style={styles.modalSectionText}>
              {source.journal}
              {source.year && ` (${source.year})`}
            </Text>
          </View>
        )}

        {source.excerpt && (
          <View style={styles.modalSection}>
            <Text style={styles.modalSectionTitle}>Relevant Excerpt</Text>
            <Text style={styles.modalSectionText}>{source.excerpt}</Text>
          </View>
        )}

        <View style={styles.modalSection}>
          <Text style={styles.modalSectionTitle}>Evidence Quality</Text>
          <Text style={styles.modalSectionText}>
            Grade {source.evidenceLevel} -{' '}
            {source.evidenceLevel && EVIDENCE_LEVELS[source.evidenceLevel as keyof typeof EVIDENCE_LEVELS] ? EVIDENCE_LEVELS[source.evidenceLevel as keyof typeof EVIDENCE_LEVELS].description : 'Unknown quality'}
          </Text>
        </View>

        <View style={styles.modalSection}>
          <Text style={styles.modalSectionTitle}>Relevance Score</Text>
          <Text style={styles.modalSectionText}>
            {Math.round((source.relevanceScore || 0) * 100)}% -{' '}
            {getRelevanceDescription(source.relevanceScore || 0)}
          </Text>
        </View>

        {(source.pmid || source.doi) && (
          <View style={styles.modalSection}>
            <Text style={styles.modalSectionTitle}>Identifiers</Text>
            {source.pmid && (
              <Text style={styles.modalSectionText}>PMID: {source.pmid}</Text>
            )}
            {source.doi && (
              <Text style={styles.modalSectionText}>DOI: {source.doi}</Text>
            )}
          </View>
        )}
      </ScrollView>

      {(source.url || source.pmid || source.doi) && (
        <View style={styles.modalFooter}>
          <TouchableOpacity style={styles.openUrlButton} onPress={onOpenUrl}>
            <Ionicons name="open-outline" size={20} color={COLORS.white} />
            <Text style={styles.openUrlText}>Open Source</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

function mapSourceTypeToVerificationLevel(
  sourceType: string
): VerificationLevel {
  switch (sourceType.toUpperCase()) {
    case 'FDA':
      return 'A';
    case 'NIH':
      return 'A';
    case 'PUBMED':
      return 'B';
    case 'CLINICAL_TRIAL':
      return 'B';
    case 'TEXTBOOK':
      return 'C';
    case 'GUIDELINE':
      return 'C';
    default:
      return 'D';
  }
}

function getSourceTypeConfig(type?: VerificationLevel) {
  switch (type) {
    case 'A':
      return { label: 'Grade A', color: COLORS.success };
    case 'B':
      return { label: 'Grade B', color: COLORS.primary };
    case 'C':
      return { label: 'Grade C', color: COLORS.info };
    case 'D':
      return { label: 'Grade D', color: COLORS.warning };
    case 'E':
      return { label: 'Grade E', color: COLORS.secondary };
    default:
      return { label: 'Unknown', color: COLORS.gray500 };
  }
}

function getRelevanceDescription(score: number) {
  if (score >= 0.8) return 'Highly relevant to your query';
  if (score >= 0.6) return 'Moderately relevant to your query';
  if (score >= 0.4) return 'Somewhat relevant to your query';
  return 'Limited relevance to your query';
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.backgroundSecondary,
    borderRadius: 12,
    padding: SPACING.md,
    marginVertical: SPACING.sm,
  },
  compactContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.xs,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  headerText: {
    fontSize: TYPOGRAPHY.sizes.base,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.primary,
    marginLeft: SPACING.xs,
  },
  countText: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
    marginLeft: SPACING.xs,
  },
  compactText: {
    fontSize: TYPOGRAPHY.sizes.xs,
    color: COLORS.textSecondary,
    marginLeft: SPACING.xs,
  },
  viewAllText: {
    fontSize: TYPOGRAPHY.sizes.xs,
    color: COLORS.primary,
    marginLeft: SPACING.sm,
  },
  loadingText: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  sourceItem: {
    backgroundColor: COLORS.white,
    borderRadius: 8,
    padding: SPACING.sm,
    marginBottom: SPACING.xs,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  sourceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  sourceNumber: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: COLORS.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: SPACING.xs,
  },
  sourceNumberText: {
    fontSize: TYPOGRAPHY.sizes.xs,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: COLORS.white,
  },
  sourceTitle: {
    fontSize: TYPOGRAPHY.sizes.sm,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
    lineHeight: TYPOGRAPHY.lineHeights.normal * TYPOGRAPHY.sizes.sm,
  },
  sourceAuthors: {
    fontSize: TYPOGRAPHY.sizes.xs,
    color: COLORS.textSecondary,
    marginBottom: SPACING.xs,
  },
  sourceJournal: {
    fontSize: TYPOGRAPHY.sizes.xs,
    color: COLORS.textSecondary,
    fontStyle: 'italic',
    marginBottom: SPACING.xs,
  },
  sourceFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  relevanceScore: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  relevanceText: {
    fontSize: TYPOGRAPHY.sizes.xs,
    color: COLORS.textSecondary,
    marginLeft: SPACING.xs,
  },
  showMoreButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.sm,
  },
  showMoreText: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.primary,
    marginRight: SPACING.xs,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.md,
    paddingTop: SPACING.xxl,
    paddingBottom: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  modalTitle: {
    fontSize: TYPOGRAPHY.sizes.lg,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
  },
  placeholder: {
    width: 24,
  },
  modalContent: {
    flex: 1,
    paddingHorizontal: SPACING.md,
  },
  modalSourceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: SPACING.md,
  },
  modalSourceTitle: {
    fontSize: TYPOGRAPHY.sizes.xl,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: COLORS.textPrimary,
    lineHeight: TYPOGRAPHY.lineHeights.normal * TYPOGRAPHY.sizes.xl,
    marginBottom: SPACING.md,
  },
  modalSection: {
    marginBottom: SPACING.md,
  },
  modalSectionTitle: {
    fontSize: TYPOGRAPHY.sizes.base,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.sm,
  },
  modalSectionText: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
    lineHeight: TYPOGRAPHY.lineHeights.normal * TYPOGRAPHY.sizes.sm,
  },
  modalFooter: {
    paddingHorizontal: SPACING.md,
    paddingBottom: SPACING.xl,
    paddingTop: SPACING.md,
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
  },
  openUrlButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.primary,
    paddingVertical: SPACING.md,
    borderRadius: 12,
  },
  openUrlText: {
    fontSize: TYPOGRAPHY.sizes.base,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.white,
    marginLeft: SPACING.sm,
  },
});
