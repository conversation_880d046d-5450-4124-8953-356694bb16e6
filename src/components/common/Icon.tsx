// src/components/common/Icon.tsx
import React from 'react';
import { Ionicons } from '@expo/vector-icons';
import { ViewStyle } from 'react-native';

// Type-safe icon names from Ionicons
export type IconName = keyof typeof Ionicons.glyphMap;

interface IconProps {
  name: IconName;
  size?: number;
  color?: string;
  style?: ViewStyle;
  testID?: string;
}

/**
 * Type-safe Icon wrapper component
 * Provides proper TypeScript support for icon names
 */
export const Icon: React.FC<IconProps> = ({ 
  name, 
  size = 24, 
  color = '#000',
  style,
  testID
}) => {
  return (
    <Ionicons 
      name={name} 
      size={size} 
      color={color} 
      style={style}
      testID={testID}
    />
  );
};

// Common icon presets for consistency
export const IconPresets = {
  // Tab bar icons
  tabHome: { name: 'home-outline' as IconName, size: 24 },
  tabHomeActive: { name: 'home' as IconName, size: 24 },
  tabScan: { name: 'scan-outline' as IconName, size: 24 },
  tabScanActive: { name: 'scan' as IconName, size: 24 },
  tabStack: { name: 'layers-outline' as IconName, size: 24 },
  tabStackActive: { name: 'layers' as IconName, size: 24 },
  tabChat: { name: 'chatbubble-outline' as IconName, size: 24 },
  tabChatActive: { name: 'chatbubble' as IconName, size: 24 },
  tabProfile: { name: 'person-outline' as IconName, size: 24 },
  tabProfileActive: { name: 'person' as IconName, size: 24 },
  
  // Common UI icons
  back: { name: 'arrow-back' as IconName, size: 24 },
  forward: { name: 'arrow-forward' as IconName, size: 24 },
  close: { name: 'close' as IconName, size: 24 },
  menu: { name: 'menu' as IconName, size: 24 },
  search: { name: 'search' as IconName, size: 20 },
  settings: { name: 'settings' as IconName, size: 24 },
  
  // Actions
  add: { name: 'add' as IconName, size: 24 },
  remove: { name: 'remove' as IconName, size: 24 },
  edit: { name: 'create' as IconName, size: 20 },
  delete: { name: 'trash' as IconName, size: 20 },
  save: { name: 'checkmark' as IconName, size: 24 },
  share: { name: 'share' as IconName, size: 20 },
  copy: { name: 'copy' as IconName, size: 20 },
  
  // Status
  success: { name: 'checkmark-circle' as IconName, size: 24 },
  warning: { name: 'warning' as IconName, size: 24 },
  error: { name: 'close-circle' as IconName, size: 24 },
  info: { name: 'information-circle' as IconName, size: 24 },
  
  // Medical/Health
  medical: { name: 'medkit' as IconName, size: 24 },
  heart: { name: 'heart' as IconName, size: 20 },
  shield: { name: 'shield-checkmark' as IconName, size: 20 },
  
  // Scanning
  camera: { name: 'camera' as IconName, size: 24 },
  barcode: { name: 'barcode' as IconName, size: 24 },
  
  // Data
  cloud: { name: 'cloud' as IconName, size: 20 },
  download: { name: 'download' as IconName, size: 20 },
  upload: { name: 'cloud-upload' as IconName, size: 20 },
  
  // Communication
  mail: { name: 'mail' as IconName, size: 20 },
  call: { name: 'call' as IconName, size: 20 },
  help: { name: 'help-circle' as IconName, size: 20 },
};