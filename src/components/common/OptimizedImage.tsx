// src/components/common/OptimizedImage.tsx
import React, { useState } from 'react';
import { View, StyleSheet, ViewStyle, ActivityIndicator, Image } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '../../constants';

interface IOptimizedImageProps {
  source: { uri: string } | number;
  style?: ViewStyle;
  placeholder?: React.ReactNode;
  fallbackIcon?: keyof typeof Ionicons.glyphMap;
  fallbackIconSize?: number;
  resizeMode?: 'cover' | 'contain' | 'stretch' | 'repeat' | 'center';
  onLoad?: () => void;
  onError?: () => void;
}

export function OptimizedImage({
  source,
  style,
  placeholder,
  fallbackIcon = 'alert-circle',
  fallbackIconSize = 24,
  resizeMode = 'contain',
  onLoad,
  onError,
}: IOptimizedImageProps): React.ReactElement {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  const handleLoad = () => {
    setIsLoading(false);
    setHasError(false);
    onLoad?.();
  };

  const handleError = () => {
    setIsLoading(false);
    setHasError(true);
    onError?.();
  };

  const renderPlaceholder = () => {
    if (placeholder) {
      return placeholder;
    }

    if (hasError) {
      return (
        <View style={[styles.placeholder, style]}>
          <Ionicons
            name={fallbackIcon}
            size={fallbackIconSize}
            color={COLORS.gray400}
          />
        </View>
      );
    }

    if (isLoading) {
      return (
        <View style={[styles.placeholder, style]}>
          <ActivityIndicator size="small" color={COLORS.primary} />
        </View>
      );
    }

    return null;
  };

  // Early validation of source prop
  if (!source || (typeof source === 'object' && !source.uri)) {
    return (
      <View style={[styles.placeholder, style]}>
        <Ionicons
          name={fallbackIcon}
          size={fallbackIconSize}
          color={COLORS.gray400}
        />
      </View>
    );
  }

  return (
    <View style={style}>
      <Image
        source={source}
        style={[
          StyleSheet.absoluteFillObject,
          { opacity: isLoading || hasError ? 0 : 1 },
        ]}
        resizeMode={resizeMode}
        onLoad={handleLoad}
        onError={handleError}
      />
      {renderPlaceholder()}
    </View>
  );
}

const styles = StyleSheet.create({
  placeholder: {
    backgroundColor: COLORS.gray100,
    justifyContent: 'center',
    alignItems: 'center',
  },
});