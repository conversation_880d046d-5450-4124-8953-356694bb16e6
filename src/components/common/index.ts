// src/components/common/index.ts

export { Button } from './Button';
export { AnimatedTouchable } from './AnimatedTouchable';
export { Input } from './Input';
export { CustomHeader } from './CustomHeader';
export { CustomFAB } from './CustomFAB';
export { OptimizedImage } from './OptimizedImage';
export { EnhancedOptimizedImage } from './EnhancedOptimizedImage';
export { OptimizedIcon, PresetIcon, IconPresets } from './OptimizedIcon';
export { Icon, IconPresets as IconPresetsNew } from './Icon';
export type { IconName } from './Icon';
export { AnimatedScore, AnimatedCounter } from './AnimatedCounter';
export { NetworkStatusBanner } from './NetworkStatusBanner';
export { OfflineModeScreen } from './OfflineModeScreen';
export {
  LazyScreenWrapper,
  withLazyLoading,
  createLazyScreen,
  preloadScreen,
} from './LazyScreen';
