// src/components/home/<USER>
import React, { useCallback, useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  ViewToken,
  Dimensions,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { COLORS, SPACING, TYPOGRAPHY, APP_CONFIG } from '../../constants/index';
import { SourceCitation } from '../analysis/SourceCitation';
import { multiSourceDataService } from '../../services/dataIntegration/multiSourceDataService';
import { gamificationService } from '../../services/gamification/gamificationService';
import type { RecentScan, DatabaseSourceReference } from '../../types';
import { SupplementCard } from './SupplementCard';

interface RecentScansCarouselProps {
  recentScans: RecentScan[];
  loading: boolean;
  onScanPress: (scan: RecentScan) => void;
  onScanAnother: () => void;
  onHelpfulClick?: (supplement: any, isHelpful: boolean) => void;
}

export const RecentScansCarousel: React.FC<RecentScansCarouselProps> = ({
  recentScans,
  loading,
  onScanPress,
  onScanAnother,
  onHelpfulClick,
}) => {
  const [visibleItems, setVisibleItems] = useState(new Set<string>());
  const [sourcesMap, setSourcesMap] = useState<
    Map<string, DatabaseSourceReference[]>
  >(new Map());
  const screenWidth = Dimensions.get('window').width;
  const cardWidth = Math.min(screenWidth * 0.85, 320);

  useEffect(() => {
    const fetchSources = async () => {
      const newSourcesMap = new Map<string, DatabaseSourceReference[]>();
      for (const scan of recentScans) {
        if (scan.hasInteraction && visibleItems.has(scan.id)) {
          const sources = await multiSourceDataService.searchPubMedEvidence(
            scan.name
          );
          newSourcesMap.set(scan.id, sources);
        }
      }
      setSourcesMap(newSourcesMap);
    };
    fetchSources();
  }, [recentScans, visibleItems]);

  const onViewableItemsChanged = useCallback(
    ({ viewableItems }: { viewableItems: ViewToken[] }) => {
      const newVisibleItems = new Set(
        viewableItems.map(item => item.item?.id).filter(Boolean)
      );
      setVisibleItems(newVisibleItems);
    },
    []
  );

  const viewabilityConfig = {
    itemVisiblePercentThreshold: 50,
    minimumViewTime: 100,
  };

  const convertScanToSupplement = (scan: RecentScan) => ({
    id: scan.id,
    name: scan.name,
    brand: scan.brand,
    imageUrl: scan.imageUrl,
    rating: scan.score / 20,
    score: scan.score,
    riskStatus: scan.hasInteraction
      ? scan.score < 60
        ? 'High Risk'
        : 'Caution'
      : ('Safe' as 'Safe' | 'Caution' | 'High Risk'),
    evidence: scan.evidence,
    dosage: scan.dosage,
    description: scan.description,
  });

  const renderScanCard = ({ item }: { item: RecentScan }) => {
    const supplement = convertScanToSupplement(item);
    const sources = sourcesMap.get(item.id) || [];

    return (
      <View style={[styles.cardContainer, { width: cardWidth }]}>
        <SupplementCard
          supplement={supplement}
          onPress={() => onScanPress(item)}
          onHelpfulClick={onHelpfulClick}
        />
        {item.hasInteraction && sources.length > 0 && (
          <View style={styles.sourcesContainer}>
            <Text style={styles.sourcesTitle}>Evidence Sources</Text>
            {sources.slice(0, 2).map((source, index) => (
              <SourceCitation
                key={index}
                source={source}
                style={styles.sourceItem}
              />
            ))}
          </View>
        )}
      </View>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <View style={styles.emptyIcon}>
        <Ionicons name="scan-outline" size={48} color={COLORS.textSecondary} />
      </View>
      <Text style={styles.emptyTitle}>No recent scans</Text>
      <Text style={styles.emptySubtitle}>
        Start scanning supplements to see them here
      </Text>
      <TouchableOpacity
        style={styles.scanButton}
        onPress={async () => {
          onScanAnother();
          await gamificationService.awardPoints('DAILY_SCAN', {});
        }}
        activeOpacity={0.8}
      >
        <Ionicons name="scan" size={20} color={COLORS.white} />
        <Text style={styles.scanButtonText}>Scan Now</Text>
      </TouchableOpacity>
    </View>
  );

  const renderLoadingSkeleton = () => (
    <View style={styles.skeletonContainer}>
      {[1, 2, 3].map(index => (
        <View 
          key={index} 
          style={[styles.skeletonCard, { width: cardWidth, marginRight: SPACING.sm }]}
        >
          <ActivityIndicator size="small" color={COLORS.primary} />
        </View>
      ))}
    </View>
  );

  if (loading) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Recent Scans</Text>
        </View>
        {renderLoadingSkeleton()}
      </View>
    );
  }

  const carouselScans = recentScans.slice(0, APP_CONFIG.MAX_RECENT_SCANS);

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Recent Scans</Text>
        {recentScans.length > 0 && (
          <TouchableOpacity onPress={onScanAnother}>
            <Text style={styles.seeAllText}>Scan Another</Text>
          </TouchableOpacity>
        )}
        {recentScans.length > APP_CONFIG.MAX_RECENT_SCANS && (
          <TouchableOpacity
            onPress={() =>
              alert('Show more scans (TODO: implement modal/screen)')
            }
          >
            <Text style={styles.seeAllText}>View More</Text>
          </TouchableOpacity>
        )}
      </View>

      {recentScans.length === 0 ? (
        renderEmptyState()
      ) : (
        <FlatList
          data={carouselScans}
          renderItem={renderScanCard}
          keyExtractor={item => item.id}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.listContainer}
          snapToInterval={cardWidth + SPACING.sm}
          decelerationRate="fast"
          snapToAlignment="start"
          onViewableItemsChanged={onViewableItemsChanged}
          viewabilityConfig={viewabilityConfig}
          removeClippedSubviews={true}
          maxToRenderPerBatch={3}
          windowSize={5}
          initialNumToRender={2}
          getItemLayout={(_, index) => ({
            length: cardWidth + SPACING.sm,
            offset: (cardWidth + SPACING.sm) * index,
            index,
          })}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: SPACING.lg,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    marginBottom: SPACING.md,
  },
  title: {
    fontSize: TYPOGRAPHY.responsiveFontSizes.base.large,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: COLORS.textPrimary,
  },
  seeAllText: {
    fontSize: TYPOGRAPHY.responsiveFontSizes.sm.medium,
    color: COLORS.primary,
    fontWeight: TYPOGRAPHY.weights.medium,
  },
  listContainer: {
    paddingLeft: SPACING.lg,
    paddingRight: SPACING.sm,
  },
  cardContainer: {
    marginRight: SPACING.sm,
  },
  emptyState: {
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.xl,
  },
  emptyIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: COLORS.backgroundSecondary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  emptyTitle: {
    fontSize: TYPOGRAPHY.responsiveFontSizes.lg.medium,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.sm,
  },
  emptySubtitle: {
    fontSize: TYPOGRAPHY.responsiveFontSizes.base.medium,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: SPACING.xl,
  },
  scanButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.primary,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    borderRadius: 12,
    gap: SPACING.sm,
  },
  scanButtonText: {
    fontSize: TYPOGRAPHY.responsiveFontSizes.base.medium,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.white,
  },
  skeletonContainer: {
    flexDirection: 'row',
    paddingLeft: SPACING.lg,
    gap: SPACING.sm,
  },
  skeletonCard: {
    backgroundColor: COLORS.white,
    borderRadius: 16,
    padding: SPACING.lg,
    elevation: 2,
    shadowColor: COLORS.shadowDark,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  skeletonHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.md,
  },
  skeletonTitle: {
    width: '60%',
    height: 20,
    backgroundColor: COLORS.gray200,
    borderRadius: 4,
  },
  skeletonImage: {
    width: 48,
    height: 48,
    backgroundColor: COLORS.gray200,
    borderRadius: 8,
  },
  skeletonRating: {
    width: '40%',
    height: 16,
    backgroundColor: COLORS.gray200,
    borderRadius: 4,
    marginBottom: SPACING.sm,
  },
  skeletonStatus: {
    width: '50%',
    height: 24,
    backgroundColor: COLORS.gray200,
    borderRadius: 12,
    marginBottom: SPACING.md,
  },
  skeletonDescription: {
    width: '80%',
    height: 40,
    backgroundColor: COLORS.gray200,
    borderRadius: 4,
  },
  sourcesContainer: {
    marginTop: SPACING.sm,
    padding: SPACING.sm,
    backgroundColor: COLORS.backgroundSecondary,
    borderRadius: 8,
  },
  sourcesTitle: {
    fontSize: TYPOGRAPHY.responsiveFontSizes.sm.medium,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
  },
  sourceItem: {
    marginBottom: SPACING.xs,
  },
});
