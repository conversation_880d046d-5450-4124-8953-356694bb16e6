// src/components/scan/InteractionAlert.tsx
import React, { useEffect } from 'react';
import { View, Text, StyleSheet, Animated } from 'react-native';
import { supabase } from '../../lib/supabase';
import { COLORS, SPACING, TYPOGRAPHY, ANIMATIONS } from '../../constants/index';
import type { StackInteractionResult } from '../../types';

interface InteractionAlertProps {
  stackInteraction: StackInteractionResult;
}

export const InteractionAlert: React.FC<InteractionAlertProps> = ({
  stackInteraction,
}) => {
  const fadeAnim = new Animated.Value(0);

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: ANIMATIONS.DURATION.normal,
      easing: ANIMATIONS.EASING.easeInOut,
      useNativeDriver: true,
    }).start();

    const logInteractionView = async () => {
      try {
        await supabase.from('analytics_events').insert({
          user_id: 'local', // Replace with actual user_id from auth context
          action: 'view_interaction_alert',
          metadata: {
            interactions: stackInteraction.interactions,
            nutrientWarnings: stackInteraction.nutrientWarnings,
            timestamp: new Date().toISOString(),
          },
        });
      } catch (error) {
        console.error('Error logging interaction view:', error);
      }
    };
    logInteractionView();
  }, [stackInteraction]);

  const renderWarnings = () => {
    return (
      <Animated.View
        style={[styles.nutrientWarningsContainer, { opacity: fadeAnim }]}
      >
        {stackInteraction.nutrientWarnings.map((warning, index) => (
          <Text key={`nutrient-${index}`} style={styles.warningText}>
            {warning.nutrient}: {warning.percentOfLimit.toFixed(1)}% of UL -{' '}
            {warning.recommendation}
          </Text>
        ))}
        {stackInteraction.interactions.map((interaction, index) => (
          <Text
            key={`interaction-${index}`}
            style={[
              styles.warningText,
              interaction.severity === 'CRITICAL' && styles.criticalWarning,
            ]}
          >
            {interaction.substance1.name} - {interaction.substance2.name}:{' '}
            {interaction.description} ({interaction.severity})
          </Text>
        ))}
      </Animated.View>
    );
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>
        Interaction Risk: {stackInteraction.overallRiskLevel}
      </Text>
      {renderWarnings()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.backgroundSecondary,
    padding: SPACING.md,
    borderRadius: 8,
    marginBottom: SPACING.lg,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  title: {
    fontSize: TYPOGRAPHY.sizes.lg,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.sm,
  },
  nutrientWarningsContainer: {
    marginTop: SPACING.sm,
  },
  warningText: {
    fontSize: TYPOGRAPHY.sizes.base,
    color: COLORS.warning,
    marginBottom: SPACING.xs,
  },
  criticalWarning: {
    color: COLORS.error,
    fontWeight: TYPOGRAPHY.weights.bold,
  },
});
