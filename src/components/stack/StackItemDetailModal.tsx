// src/components/stack/StackItemDetailModal.tsx

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  Modal,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Alert,
  SafeAreaView,
  ActivityIndicator, // Added for loading indicator
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { OptimizedIcon } from '../common/OptimizedIcon';
import { ValidatedInput } from '../common/ValidatedInput';
import { OptimizedImage } from '../common/OptimizedImage';
import { useFormValidation } from '../../hooks/useFormValidation';
import { COLORS, SPACING, TYPOGRAPHY, GAMIFICATION } from '../../constants'; // Added GAMIFICATION
import { multiSourceDataService } from '../../services/dataIntegration/multiSourceDataService';
import { gamificationService } from '../../services/gamification/gamificationService';
import { SourceCitation } from '../analysis/SourceCitation'; // Confirmed path from ls -R
import type {
  UserStack,
  EnhancedIngredientAnalysis,
  Product,
  DatabaseSourceReference,
} from '../../types';

interface StackItemDetailModalProps {
  visible: boolean;
  item: UserStack | null;
  onClose: () => void;
  onUpdate: (itemId: string, updates: Partial<UserStack>) => Promise<void>;
  onRemove: (itemId: string) => Promise<void>;
}

export const StackItemDetailModal: React.FC<StackItemDetailModalProps> = ({
  visible,
  item,
  onClose,
  onUpdate,
  onRemove,
}) => {
  const insets = useSafeAreaInsets();
  const [isEditing, setIsEditing] = useState(false);
  const [isLoadingContent, setIsLoadingContent] = useState(false); // New loading state for content fetch
  const [isSavingOrRemoving, setIsSavingOrRemoving] = useState(false); // Loading state for save/remove operations
  const [itemType, setItemType] = useState<'supplement' | 'medication'>(
    'supplement'
  );
  const [analysis, setAnalysis] = useState<EnhancedIngredientAnalysis[] | null>(
    null
  ); // Changed to null initially
  const [productDetails, setProductDetails] = useState<Product | null>(null);
  const [sources, setSources] = useState<DatabaseSourceReference[]>([]);

  // Form validation for editing
  const { getFieldProps, validateForm, resetForm, fields, errors, setFields } =
    useFormValidation(
      // Added setFields for dynamic updates
      {
        name: {
          validator: 'text',
          options: { required: true, maxLength: 200 },
          validateOnBlur: true,
          sanitize: true,
        },
        brand: {
          validator: 'text',
          options: { maxLength: 100 },
          validateOnBlur: true,
          sanitize: true,
        },
        dosage: {
          validator: 'dosage', // Assumes 'dosage' validator exists and is configured
          validateOnBlur: true,
          sanitize: true,
        },
        frequency: {
          validator: 'frequency', // Assumes 'frequency' validator exists and is configured
          validateOnBlur: true,
          sanitize: true,
        },
      },
      // Initial values for the form, will be updated by useEffect
      {
        name: item?.name || '',
        brand: item?.brand || '',
        dosage: item?.dosage || '',
        frequency: item?.frequency || '',
      }
    );

  // Consolidated useEffect for initialization and data fetching
  useEffect(() => {
    if (visible && item) {
      setIsLoadingContent(true);
      setItemType(item.type);
      // Reset form fields with current item's data
      setFields({
        name: item.name,
        brand: item.brand || '',
        dosage: item.dosage || '',
        frequency: item.frequency || '',
      });

      const fetchAllData = async () => {
        try {
          // Fetch detailed product data
          const fetchedProduct =
            await multiSourceDataService.fetchProductByBarcode(
              item.barcode || ''
            ); // Use item.barcode for product lookup
          setProductDetails(fetchedProduct);
          setSources(fetchedProduct?.sources || []); // Assuming product.sources comes from multiSourceDataService

          // If product barcode exists, fetch ingredient analysis
          if (fetchedProduct) {
            // Need to ensure multiSourceDataService.analyzeProduct takes a Product and returns EnhancedIngredientAnalysis[]
            // This might be a call to SupplementAnalyzer.analyzeSupplementComprehensive
            const analysisResult = await multiSourceDataService.analyzeProduct(
              fetchedProduct,
              item.user_id
            );
            setAnalysis(analysisResult);
          } else {
            // If product not found by barcode, maybe use basic item data for a limited analysis
            // This might require a simplified analysis path or clear indication
            setAnalysis(null); // Or set an empty analysis
          }

          // Award points for AI consultation (if data was fetched)
          if (fetchedProduct || analysisResult) {
            // Award points only if some data was successfully loaded
            await gamificationService.awardPoints(
              GAMIFICATION.POINTS.AI_CONSULTATION, // Using constant for points
              { item_id: item.id }
            );
          }
        } catch (error) {
          console.error('Error fetching modal data:', error);
          Alert.alert(
            'Error',
            'Failed to load item details or analysis. Please try again.'
          );
          setAnalysis(null); // Clear analysis on error
          setProductDetails(null); // Clear product details on error
          setSources([]); // Clear sources on error
        } finally {
          setIsLoadingContent(false);
        }
      };

      fetchAllData();
    } else {
      // Reset states when modal is not visible or item is null
      setIsEditing(false);
      setIsLoadingContent(false);
      setIsSavingOrRemoving(false);
      setItemType('supplement');
      setAnalysis(null);
      setProductDetails(null);
      setSources([]);
      resetForm(); // Ensure form is reset when modal closes
    }
  }, [item, visible, resetForm, setFields]); // Added setFields to dependencies

  const handleSave = async () => {
    if (!item) return;

    const isValid = await validateForm();
    if (!isValid) {
      Alert.alert('Validation Error', 'Please correct the form errors.');
      return;
    }

    setIsSavingOrRemoving(true); // Set saving loading state
    try {
      // Use fields.name.value, etc. as `fields` is an object with `value` properties
      await onUpdate(item.id, {
        name: fields.name.value,
        brand: fields.brand.value,
        dosage: fields.dosage.value,
        frequency: fields.frequency.value,
        type: itemType,
      });
      await gamificationService.awardPoints(GAMIFICATION.POINTS.STACK_UPDATE, {
        item_id: item.id,
      }); // Using constant
      setIsEditing(false);
      Alert.alert('Success', 'Item updated successfully.');
    } catch (error: any) {
      console.error('Error updating item:', error);
      Alert.alert('Error', error.message || 'Failed to update item.');
    } finally {
      setIsSavingOrRemoving(false); // Clear saving loading state
    }
  };

  const handleRemove = async () => {
    if (!item) return;
    Alert.alert(
      'Confirm Removal',
      'Are you sure you want to remove this item from your stack?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: async () => {
            setIsSavingOrRemoving(true); // Set removing loading state
            try {
              await onRemove(item.id);
              await gamificationService.awardPoints(
                GAMIFICATION.POINTS.STACK_REMOVE,
                { item_id: item.id }
              ); // Using constant
              onClose();
            } catch (error: any) {
              console.error('Error removing item:', error);
              Alert.alert('Error', error.message || 'Failed to remove item.');
            } finally {
              setIsSavingOrRemoving(false); // Clear removing loading state
            }
          },
        },
      ]
    );
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    if (item) {
      setItemType(item.type);
      setFields({
        // Reset form to original values from item prop
        name: item.name,
        brand: item.brand || '',
        dosage: item.dosage || '',
        frequency: item.frequency || '',
      });
    }
  };

  const renderIngredientAnalysis = useCallback(
    () => (
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Ingredient Analysis</Text>
        {analysis && analysis.length > 0 ? (
          analysis.map((a, index) => (
            <View
              key={`analysis-${a.ingredient}-${index}`}
              style={styles.analysisItem}
            >
              {' '}
              {/* Unique key */}
              <Text style={styles.ingredientName}>{a.ingredient}</Text>
              {a.warnings?.map(
                (
                  w,
                  i // Use optional chaining for warnings
                ) => (
                  <View
                    key={`warning-${a.ingredient}-${i}`}
                    style={styles.warning}
                  >
                    {' '}
                    {/* Unique key */}
                    <OptimizedIcon
                      type="material" // Explicitly setting type
                      name="warning"
                      size={20}
                      color={
                        w.severity === 'CRITICAL'
                          ? COLORS.error
                          : COLORS.warning
                      }
                    />
                    <Text style={styles.warningText}>
                      {w.severity}: {w.message}
                    </Text>
                  </View>
                )
              )}
              {a.recommendations?.length > 0 && ( // Use optional chaining for recommendations
                <Text style={styles.recommendation}>
                  Recommendation:{' '}
                  {a.recommendations
                    .map(rec => (typeof rec === 'string' ? rec : rec.message))
                    .join('; ')}{' '}
                  {/* Handle recommendation types */}
                </Text>
              )}
            </View>
          ))
        ) : (
          <Text style={styles.noData}>
            No detailed ingredient analysis available.
          </Text>
        )}
      </View>
    ),
    [analysis]
  ); // Memoize based on analysis

  const renderSources = useCallback(
    () => (
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Evidence Sources</Text>
        {sources.length > 0 ? (
          sources.map((source, index) => (
            <SourceCitation
              key={`source-${source.id || index}`} // Use source.id if available, fallback to index
              source={source}
              style={{ marginBottom: SPACING.sm }}
            />
          ))
        ) : (
          <Text style={styles.noData}>No external sources cited.</Text>
        )}
      </View>
    ),
    [sources]
  ); // Memoize based on sources

  if (!item) return null; // Should not happen if visible logic is correct

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet" // Keep pageSheet for iOS if desired
      onRequestClose={onClose}
      transparent={false}
    >
      <SafeAreaView style={[styles.container, { paddingTop: insets.top }]}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            onPress={onClose}
            disabled={isSavingOrRemoving || isLoadingContent} // Disable during operations
            accessibilityLabel="Close" // Accessibility
            accessibilityRole="button" // Accessibility
          >
            <OptimizedIcon
              name="close"
              size={24}
              color={COLORS.textPrimary}
              type="ion"
            />{' '}
            {/* Explicit type */}
          </TouchableOpacity>
          <Text style={styles.headerTitle}>
            {isEditing ? 'Edit Item' : 'Item Details'}
          </Text>
          <TouchableOpacity
            onPress={isEditing ? handleSave : () => setIsEditing(true)}
            disabled={isSavingOrRemoving || isLoadingContent} // Disable during operations
            accessibilityLabel={isEditing ? 'Save changes' : 'Edit item'} // Accessibility
            accessibilityRole="button" // Accessibility
          >
            <OptimizedIcon
              name={isEditing ? 'checkmark' : 'create-outline'} // Ionicons for check/edit
              size={24}
              color={COLORS.textPrimary}
              type="ion" // Explicit type
            />
          </TouchableOpacity>
        </View>

        <ScrollView
          style={styles.content}
          contentContainerStyle={{ paddingBottom: insets.bottom + SPACING.md }}
        >
          {isLoadingContent ? (
            <View style={styles.loadingOverlay}>
              <ActivityIndicator size="large" color={COLORS.primary} />
              <Text style={styles.loadingText}>Loading details...</Text>
            </View>
          ) : (
            <>
              {productDetails?.imageUrl ? (
                <OptimizedImage
                  source={{ uri: productDetails.imageUrl }}
                  style={styles.image}
                  priority="high"
                  contentFit="contain"
                  fallbackIcon="cube-outline"
                  fallbackIconSize={48}
                />
              ) : (
                <View style={styles.imagePlaceholder}>
                  <OptimizedIcon
                    type="ion" // Explicit type
                    name="cube-outline"
                    size={48}
                    color={COLORS.gray400}
                  />
                </View>
              )}

              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Basic Information</Text>
                <ValidatedInput
                  label="Name"
                  placeholder="Item name"
                  validationType="text"
                  required
                  maxLength={200}
                  editable={isEditing && !isSavingOrRemoving} // Disable while saving
                  {...getFieldProps('name')}
                  style={styles.input}
                />
                {errors.name && (
                  <Text style={styles.error}>{errors.name.message}</Text>
                )}{' '}
                {/* Access message property */}
                <ValidatedInput
                  label="Brand"
                  placeholder="Brand name"
                  validationType="text"
                  maxLength={100}
                  editable={isEditing && !isSavingOrRemoving}
                  {...getFieldProps('brand')}
                  style={styles.input}
                />
                {errors.brand && (
                  <Text style={styles.error}>{errors.brand.message}</Text>
                )}
                <View style={styles.field}>
                  <Text style={styles.fieldLabel}>Type</Text>
                  {isEditing ? (
                    <View style={styles.typeSelector}>
                      <TouchableOpacity
                        onPress={() => setItemType('supplement')}
                        disabled={isSavingOrRemoving}
                        style={[
                          styles.typeButton,
                          itemType === 'supplement' && styles.typeButtonActive,
                        ]}
                      >
                        <Text
                          style={[
                            styles.typeButtonText,
                            itemType === 'supplement' &&
                              styles.typeButtonTextActive,
                          ]}
                        >
                          Supplement
                        </Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        onPress={() => setItemType('medication')}
                        disabled={isSavingOrRemoving}
                        style={[
                          styles.typeButton,
                          itemType === 'medication' && styles.typeButtonActive,
                        ]}
                      >
                        <Text
                          style={[
                            styles.typeButtonText,
                            itemType === 'medication' &&
                              styles.typeButtonTextActive,
                          ]}
                        >
                          Medication
                        </Text>
                      </TouchableOpacity>
                    </View>
                  ) : (
                    <Text style={styles.fieldValue}>
                      {item.type.charAt(0).toUpperCase() + item.type.slice(1)}
                    </Text>
                  )}
                </View>
              </View>

              {/* Dosage Information */}
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Dosage Information</Text>
                <ValidatedInput
                  label="Dosage"
                  placeholder="e.g., 500mg, 1 tablet"
                  validationType="text" // Assuming validator handles dosage string
                  required
                  maxLength={100}
                  editable={isEditing && !isSavingOrRemoving}
                  {...getFieldProps('dosage')}
                  style={styles.input}
                />
                {errors.dosage && (
                  <Text style={styles.error}>{errors.dosage.message}</Text>
                )}

                <ValidatedInput
                  label="Frequency"
                  placeholder="e.g., Once daily, Twice daily"
                  validationType="text" // Assuming validator handles frequency string
                  required
                  maxLength={100}
                  editable={isEditing && !isSavingOrRemoving}
                  {...getFieldProps('frequency')}
                  style={styles.input}
                />
                {errors.frequency && (
                  <Text style={styles.error}>{errors.frequency.message}</Text>
                )}
              </View>

              {/* Ingredients (read-only from item.ingredients, potentially enriched by productDetails if available) */}
              {item.ingredients && item.ingredients.length > 0 && (
                <View style={styles.section}>
                  <Text style={styles.sectionTitle}>Raw Ingredients</Text>
                  {item.ingredients.map((ingredient, index) => (
                    <View
                      key={`raw-ingredient-${index}`}
                      style={styles.ingredientItem}
                    >
                      {' '}
                      {/* Specific key */}
                      <Text style={styles.ingredientName}>
                        {ingredient.name}
                      </Text>
                      {ingredient.amount && ingredient.unit && (
                        <Text style={styles.ingredientAmount}>
                          {ingredient.amount}
                          {ingredient.unit}
                        </Text>
                      )}
                    </View>
                  ))}
                </View>
              )}

              {/* Enhanced Ingredient Analysis (if available) */}
              {renderIngredientAnalysis()}

              {/* Evidence Sources */}
              {renderSources()}

              {isEditing && (
                <View style={styles.actions}>
                  <TouchableOpacity
                    style={[styles.button, styles.saveButton]}
                    onPress={handleSave}
                    disabled={isSavingOrRemoving || isLoadingContent} // Disable if saving or loading
                  >
                    {isSavingOrRemoving ? (
                      <ActivityIndicator color={COLORS.white} />
                    ) : (
                      <Text style={styles.buttonText}>Save Changes</Text>
                    )}
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[styles.button, styles.cancelButton]} // Reusing cancel button for consistent styling
                    onPress={handleCancelEdit}
                    disabled={isSavingOrRemoving || isLoadingContent}
                  >
                    <Text style={styles.cancelButtonText}>Cancel</Text>
                  </TouchableOpacity>
                </View>
              )}
            </>
          )}
        </ScrollView>

        {/* Fixed Footer with Remove Button (visible only in view mode) */}
        {!isEditing && (
          <View
            style={[
              styles.footer,
              { paddingBottom: insets.bottom + SPACING.xl },
            ]}
          >
            <TouchableOpacity
              style={styles.removeButton}
              onPress={handleRemove}
              disabled={isSavingOrRemoving || isLoadingContent} // Disable during operations
              accessibilityLabel="Remove item from stack" // Accessibility
              accessibilityRole="button" // Accessibility
            >
              {isSavingOrRemoving ? (
                <ActivityIndicator color={COLORS.white} />
              ) : (
                <>
                  <OptimizedIcon
                    type="material" // Explicit type
                    name="delete"
                    size={20}
                    color={COLORS.white}
                  />
                  <Text style={styles.removeButtonText}>Remove from Stack</Text>
                </>
              )}
            </TouchableOpacity>
          </View>
        )}
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  backButton: {
    // Removed from JSX, but keeping style if it was meant for general back buttons
    padding: SPACING.sm,
    marginLeft: -SPACING.xs,
    backgroundColor: COLORS.backgroundSecondary,
    borderRadius: 8,
    minWidth: 44,
    minHeight: 44,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: COLORS.primary,
  },
  headerTitle: {
    fontSize: TYPOGRAPHY.sizes.lg,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: COLORS.textPrimary,
  },
  actionButton: {
    // Style for the header edit/save button
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.xs,
  },
  actionButtonText: {
    fontSize: TYPOGRAPHY.sizes.base,
    fontWeight: TYPOGRAPHY.weights.medium,
    color: COLORS.primary,
  },
  content: {
    flex: 1,
    padding: SPACING.lg,
  },
  scrollContent: {
    // Not directly used in JSX, but keeping for reference if it was a prev contentContainerStyle
    paddingBottom: SPACING.md,
  },
  footer: {
    padding: SPACING.lg,
    paddingBottom: SPACING.xl,
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
    backgroundColor: COLORS.background,
  },
  imageContainer: {
    // Not directly used in JSX, but keeping style if it was meant for general image containers
    alignItems: 'center',
    marginBottom: SPACING.xl,
  },
  itemImage: {
    // Not directly used in JSX, but keeping style if it was meant for general images
    width: 120,
    height: 120,
    borderRadius: 12,
  },
  imagePlaceholder: {
    width: '100%', // Max width
    height: 200, // Fixed height for consistent image/placeholder size
    borderRadius: 8,
    backgroundColor: COLORS.gray100,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.md, // Added margin bottom for spacing
  },
  image: {
    // Style for the fetched image
    width: '100%',
    height: 200,
    borderRadius: 8,
    marginBottom: SPACING.md,
  },
  section: {
    marginBottom: SPACING.xl,
  },
  sectionTitle: {
    fontSize: TYPOGRAPHY.sizes.lg,
    fontWeight: TYPOGRAPHY.weights.bold, // Using TYPOGRAPHY.weights.bold consistently
    color: COLORS.textPrimary,
    marginBottom: SPACING.md,
  },
  field: {
    // Used for non-editable fields
    marginBottom: SPACING.md,
  },
  fieldLabel: {
    fontSize: TYPOGRAPHY.sizes.sm,
    fontWeight: TYPOGRAPHY.weights.medium,
    color: COLORS.textSecondary,
    marginBottom: SPACING.xs,
  },
  fieldValue: {
    fontSize: TYPOGRAPHY.sizes.base,
    color: COLORS.textPrimary,
  },
  input: {
    marginBottom: SPACING.sm, // Apply margin directly to ValidatedInput instance
    // ValidatedInput typically has its own internal styling for border, padding, font.
    // If it doesn't, add these back as part of its internal styles or specific overrides.
  },
  error: {
    // Style for validation errors
    color: COLORS.error,
    fontSize: TYPOGRAPHY.sizes.sm,
    marginBottom: SPACING.sm,
  },
  typeSelector: {
    flexDirection: 'row',
    alignItems: 'center', // Align label and buttons
    marginBottom: SPACING.sm, // Add margin bottom
  },
  label: {
    // New style for the 'Type' label
    fontSize: TYPOGRAPHY.sizes.base,
    color: COLORS.textPrimary,
    marginRight: SPACING.sm,
  },
  typeButton: {
    padding: SPACING.sm,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: COLORS.border,
    marginRight: SPACING.sm,
    alignItems: 'center',
  },
  typeButtonActive: {
    backgroundColor: COLORS.primary,
    borderColor: COLORS.primary,
  },
  typeButtonText: {
    fontSize: TYPOGRAPHY.sizes.sm,
    fontWeight: TYPOGRAPHY.weights.medium,
    color: COLORS.textPrimary,
  },
  typeButtonTextActive: {
    color: COLORS.textInverse,
  },
  ingredientItem: {
    // For raw ingredients list
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.xs,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  ingredientName: {
    fontSize: TYPOGRAPHY.sizes.base,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    flex: 1, // Allow name to take available space
  },
  ingredientAmount: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
    fontWeight: TYPOGRAPHY.weights.medium,
  },
  removeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.error,
    paddingVertical: SPACING.md,
    borderRadius: 8,
    gap: SPACING.sm,
  },
  removeButtonText: {
    fontSize: TYPOGRAPHY.sizes.base,
    fontWeight: TYPOGRAPHY.weights.medium,
    color: COLORS.white,
  },
  actions: {
    // Container for Save/Cancel buttons
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: SPACING.lg,
  },
  button: {
    flex: 1,
    padding: SPACING.md,
    borderRadius: 8, // Consistent with other buttons
    alignItems: 'center',
    marginHorizontal: SPACING.xs,
  },
  saveButton: {
    backgroundColor: COLORS.primary,
  },
  cancelButton: {
    // Reusing for consistency
    backgroundColor: COLORS.backgroundSecondary, // Changed from COLORS.border to blend better
    borderColor: COLORS.border,
    borderWidth: 1,
  },
  cancelButtonText: {
    // Reusing for consistency
    fontSize: TYPOGRAPHY.sizes.base,
    fontWeight: TYPOGRAPHY.weights.medium,
    color: COLORS.textPrimary,
  },
  analysisItem: {
    // For ingredient analysis display
    padding: SPACING.sm,
    borderRadius: 4,
    backgroundColor: COLORS.backgroundSecondary,
    marginBottom: SPACING.sm,
  },
  warning: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  warningText: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.error, // Assuming warning is error color here
    marginLeft: SPACING.xs,
    flexShrink: 1, // Allow text to wrap
  },
  recommendation: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.success,
    marginTop: SPACING.xs,
  },
  noData: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
    fontStyle: 'italic',
    textAlign: 'center', // Center no data message
  },
  loadingOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 200, // Ensure it takes some space
  },
  loadingText: {
    marginTop: SPACING.md,
    fontSize: TYPOGRAPHY.sizes.base,
    color: COLORS.textSecondary,
  },
});
