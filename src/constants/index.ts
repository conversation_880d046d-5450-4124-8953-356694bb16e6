// src/constants/index.ts

import { Interaction, UserStack } from '../types';

export const COLORS = {
  primary: '#2563EB',
  primaryDark: '#1D4ED8',
  primaryLight: '#60A5FA',
  secondary: '#10B981',
  secondaryDark: '#059669',
  accent: '#F59E0B',
  error: '#EF4444',
  errorLight: '#FEF2F2',
  warning: '#F97316',
  warningLight: '#FFF7ED',
  success: '#22C55E',
  successLight: '#F0FDF4',
  info: '#3B82F6',
  infoLight: '#EFF6FF',

  // Grays
  gray50: '#F9FAFB',
  gray100: '#F3F4F6',
  gray200: '#E5E7EB',
  gray300: '#D1D5DB',
  gray400: '#9CA3AF',
  gray500: '#6B7280',
  gray600: '#4B5563',
  gray700: '#374151',
  gray800: '#1F2937',
  gray900: '#111827',

  // Backgrounds
  background: '#FFFFFF',
  backgroundSecondary: '#F9FAFB',
  backgroundTertiary: '#F3F4F6',
  surface: '#FFFFFF',
  white: '#FFFFFF',

  // Glass morphism colors
  glass: 'rgba(255, 255, 255, 0.7)',
  glassLight: 'rgba(255, 255, 255, 0.9)',
  glassDark: 'rgba(0, 0, 0, 0.1)',
  glassBlur: 'rgba(255, 255, 255, 0.25)',

  // Borders
  border: '#E5E7EB',
  borderLight: 'rgba(229, 231, 235, 0.5)',
  borderGlass: 'rgba(255, 255, 255, 0.2)',

  // Text
  textPrimary: '#111827',
  textSecondary: '#6B7280',
  textTertiary: '#9CA3AF',
  textInverse: '#FFFFFF',

  // Interactive states
  hover: 'rgba(37, 99, 235, 0.1)',
  pressed: 'rgba(37, 99, 235, 0.2)',
  disabled: '#E5E7EB',

  // Shadows
  shadowLight: 'rgba(0, 0, 0, 0.05)',
  shadowMedium: 'rgba(0, 0, 0, 0.1)',
  shadowDark: 'rgba(0, 0, 0, 0.2)',
  shadowPrimary: 'rgba(37, 99, 235, 0.25)',
  shadowGlow: 'rgba(37, 99, 235, 0.4)',
};

export const SPACING = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
  xxxl: 64,
};

export const TYPOGRAPHY = {
  sizes: {
    xs: 12,
    sm: 14,
    base: 16,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
    xxxl: 30,
    xxxxl: 36,
  },
  weights: {
    regular: '400' as const,
    medium: '500' as const,
    semibold: '600' as const,
    bold: '700' as const,
    normal: '400',
  } as const,
  lineHeights: {
    tight: 1.2,
    normal: 1.5,
    relaxed: 1.75,
  },
  responsiveFontSizes: {
    xs: { small: 10, medium: 12, large: 14 },
    sm: { small: 12, medium: 14, large: 16 },
    base: { small: 14, medium: 16, large: 18 },
    md: { small: 14, medium: 16, large: 18 },
    lg: { small: 16, medium: 18, large: 20 },
    xl: { small: 18, medium: 20, large: 22 },
    xxl: { small: 20, medium: 24, large: 28 },
    xxxl: { small: 26, medium: 30, large: 34 },
    xxxxl: { small: 32, medium: 36, large: 40 },
  },
};

export const SHADOWS = {
  sm: {
    shadowColor: COLORS.shadowMedium,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  md: {
    shadowColor: COLORS.shadowMedium,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 3,
    elevation: 4,
  },
  lg: {
    shadowColor: COLORS.shadowDark,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 5,
    elevation: 8,
  },
};

export const API_ENDPOINTS = {
  HUGGINGFACE: 'https://api-inference.huggingface.co/models',
  HUGGINGFACE_MODELS: 'https://api-inference.huggingface.co/models',
  OPENFOODFACTS: 'https://world.openfoodfacts.org',
  GROQ: 'https://api.groq.com/openai/v1',
  USDA: 'https://api.nal.usda.gov/fdc/v1',
  FDA: 'https://api.fda.gov',
  PubMed: 'https://eutils.ncbi.nlm.nih.gov/entrez/eutils',
  DSLD: 'https://api.ods.od.nih.gov/dsld/v9',
  OpenFoodFacts: process.env.NODE_ENV === 'development'
    ? 'https://world.openfoodfacts.net/api/v2'
    : 'https://world.openfoodfacts.org/api/v2',
  PUBMED_BACKUP: 'https://api.ncbi.nlm.nih.gov/lit/ctxp/v1/pubmed',
  OPENFDA: 'https://api.fda.gov',
  RXNORM: 'https://rxnav.nlm.nih.gov/REST',
};

export const AI_MODELS = {
  HUGGINGFACE: {
    CLASSIFICATION: { id: 'facebook/bart-large-mnli', cacheable: true },
    CLASSIFICATION_ALT: {
      id: 'typeform/distilbert-base-uncased-mnli',
      cacheable: true,
    },
    EMBEDDINGS: {
      id: 'sentence-transformers/all-MiniLM-L6-v2',
      cacheable: true,
    },
    TEXT_GENERATION: { id: 'distilgpt2', cacheable: false },
    TEXT_GENERATION_ALT: { id: 'gpt2', cacheable: false },
    TEXT_GENERATION_SMALL: { id: 'google/flan-t5-small', cacheable: false },
    FALLBACK_TEXT: { id: 'EleutherAI/gpt-neo-125M', cacheable: false },
  },
  GROQ: {
    FAST: { id: 'llama3-8b-8192:v1', cacheable: true },
    FAST_ALT: { id: 'llama-3.1-8b-instant:v1', cacheable: true },
    BALANCED: { id: 'llama3-70b-8192:v1', cacheable: true },
  },
};

// IMPORTANT: CRITICAL_INTERACTIONS and SUPPLEMENT_INTERACTIONS are now empty arrays.
// This indicates that interaction rules will be loaded dynamically (e.g., from Supabase)
// rather than being hardcoded in this file. Adjust application logic accordingly.
export const CRITICAL_INTERACTIONS: Interaction[] = [];
export const SUPPLEMENT_INTERACTIONS: Interaction[] = [];

export interface NutrientLimit {
  ul: number;
  unit: string;
  risk: string;
  populations_at_risk: string[];
  rdi?: number;
  evidenceLevel: keyof typeof EVIDENCE_LEVELS;
}

export const NUTRIENT_LIMITS: Record<string, NutrientLimit> = {
  vitamin_d: {
    rdi: 600,
    ul: 4000,
    unit: 'IU',
    risk: 'Hypercalcemia, kidney stones, heart arrhythmias',
    populations_at_risk: ['kidney disease', 'hypercalcemia', 'sarcoidosis'],
    evidenceLevel: 'A',
  },
  vitamin_a: {
    rdi: 900,
    ul: 10000,
    unit: 'IU',
    risk: 'Liver damage, birth defects (in pregnancy), bone loss',
    populations_at_risk: ['pregnancy', 'liver disease', 'smokers'],
    evidenceLevel: 'A',
  },
  iron: {
    rdi: 8,
    ul: 45,
    unit: 'mg',
    risk: 'GI distress, organ damage (liver, heart), oxidative stress',
    populations_at_risk: [
      'hemochromatosis',
      'liver disease',
      'men (unless deficient)',
    ],
    evidenceLevel: 'A',
  },
  zinc: {
    rdi: 11,
    ul: 40,
    unit: 'mg',
    risk: 'Copper deficiency, immune suppression, nausea, taste disturbances',
    populations_at_risk: ['long-term high dose users', "Wilson's disease"],
    evidenceLevel: 'A',
  },
  vitamin_e: {
    rdi: 15,
    ul: 1000,
    unit: 'mg',
    risk: 'Bleeding risk, hemorrhagic stroke (high doses)',
    populations_at_risk: [
      'blood thinners',
      'vitamin K deficiency',
      'history of stroke',
    ],
    evidenceLevel: 'A',
  },
  vitamin_b6: {
    rdi: 1.7,
    ul: 100,
    unit: 'mg',
    risk: 'Peripheral neuropathy (nerve damage, tingling, numbness)',
    populations_at_risk: ['kidney disease'],
    evidenceLevel: 'A',
  },
  magnesium: {
    rdi: 400,
    ul: 350,
    unit: 'mg',
    risk: 'Diarrhea, nausea, abdominal cramping (from supplements), hypotension (very high doses)',
    populations_at_risk: ['kidney disease', 'heart block'],
    evidenceLevel: 'A',
  },
  calcium: {
    rdi: 1000,
    ul: 2500,
    unit: 'mg',
    risk: 'Kidney stones, cardiovascular calcification, constipation',
    populations_at_risk: [
      'kidney disease',
      'hyperparathyroidism',
      'heart disease risk',
    ],
    evidenceLevel: 'A',
  },
  selenium: {
    rdi: 55,
    ul: 400,
    unit: 'mcg',
    risk: 'Hair loss, nail brittleness, neurological damage, fatigue (selenosis)',
    populations_at_risk: ['autoimmune thyroid disease (monitor)'],
    evidenceLevel: 'A',
  },
  folate: {
    rdi: 400,
    ul: 1000,
    unit: 'mcg',
    risk: 'May mask vitamin B12 deficiency symptoms, potential cognitive decline in elderly',
    populations_at_risk: ['undiagnosed B12 deficiency', 'elderly'],
    evidenceLevel: 'A',
  },
  iodine: {
    rdi: 150,
    ul: 1100,
    unit: 'mcg',
    risk: 'Thyroid dysfunction (hyper/hypothyroidism), thyroiditis, goiter',
    populations_at_risk: [
      'autoimmune thyroid disease',
      'pre-existing thyroid conditions',
    ],
    evidenceLevel: 'A',
  },
  copper: {
    rdi: 0.9,
    ul: 10,
    unit: 'mg',
    risk: 'Liver damage, neurological symptoms, gastrointestinal upset',
    populations_at_risk: ["Wilson's disease", 'liver disease'],
    evidenceLevel: 'A',
  },
  manganese: {
    rdi: 2.3,
    ul: 11,
    unit: 'mg',
    risk: "Neurological symptoms similar to Parkinson's disease",
    populations_at_risk: ['liver disease', 'iron deficiency'],
    evidenceLevel: 'B',
  },
  chromium: {
    rdi: 35,
    ul: 200,
    unit: 'mcg',
    risk: 'Liver and kidney damage (rare)',
    populations_at_risk: ['kidney disease', 'liver disease'],
    evidenceLevel: 'B',
  },
  niacin: {
    rdi: 16,
    ul: 35,
    unit: 'mg',
    risk: 'Flushing, liver toxicity, glucose intolerance',
    populations_at_risk: ['liver disease', 'diabetes', 'gout'],
    evidenceLevel: 'A',
  },
  vitamin_c: {
    rdi: 90,
    ul: 2000,
    unit: 'mg',
    risk: 'Digestive upset, kidney stones (rare)',
    populations_at_risk: ['kidney stones history', 'kidney disease'],
    evidenceLevel: 'A',
  },
  phosphorus: {
    rdi: 700,
    ul: 4000,
    unit: 'mg',
    risk: 'Kidney damage, bone problems, cardiovascular calcification',
    populations_at_risk: ['kidney disease', 'hyperparathyroidism'],
    evidenceLevel: 'A',
  },
  molybdenum: {
    rdi: 45,
    ul: 2000,
    unit: 'mcg',
    risk: 'Copper deficiency, joint pain',
    populations_at_risk: ['copper deficiency'],
    evidenceLevel: 'B',
  },
  boron: {
    rdi: 1,
    ul: 20,
    unit: 'mg',
    risk: 'Nausea, vomiting, diarrhea, skin rash',
    populations_at_risk: ['kidney disease'],
    evidenceLevel: 'B',
  },
};

export const EVIDENCE_LEVELS = {
  A: {
    label: 'Multiple RCTs',
    color: COLORS.success,
    description:
      'Supported by multiple high-quality randomized controlled trials (RCTs)',
    weight: 1.0,
    sources: ['PubMed', 'Cochrane'],
  },
  B: {
    label: 'Limited RCTs',
    color: COLORS.secondary,
    description:
      'Supported by limited randomized controlled trials or strong meta-analyses of observational studies',
    weight: 0.8,
    sources: ['PubMed', 'Cochrane'],
  },
  C: {
    label: 'Observational',
    color: COLORS.warning,
    description:
      'Supported by consistent observational studies, cohort studies, or case-control studies',
    weight: 0.6,
    sources: ['PubMed'],
  },
  D: {
    label: 'Expert Opinion',
    color: COLORS.info,
    description:
      'Based on expert consensus, mechanistic rationale, or animal/in vitro studies',
    weight: 0.4,
    sources: ['Textbooks', 'Guidelines'],
  },
  E: {
    label: 'Anecdotal',
    color: COLORS.gray500,
    description:
      'Based on personal reports or traditional use without scientific validation',
    weight: 0.2,
    sources: ['Community Reports'],
  },
};

export interface GamificationAchievement {
  id: GamificationAchievementId;
  name: string;
  description: string;
  points: number;
  icon: string;
  category: string;
  prerequisites?: GamificationAchievementId[];
}

export const GAMIFICATION = {
  POINTS: {
    DAILY_SCAN: 10,
    FIRST_SCAN: 50,
    STREAK_BONUS: 5,
    INTERACTION_FOUND: 20,
    SAFE_PRODUCT: 15,
    NEW_PRODUCT_SUBMITTED: 25,
    DATA_SUBMISSION: 50,
    DATA_VERIFICATION: 25,
    HELPFUL_RATING: 10,
    REFERRAL_SUCCESS: 100,
    SHARE_STACK: 20,
    AI_CONSULTATION: 5,
    AI_ANSWER_HELPFUL: 10,
    AI_ADVICE_SHARED: 20,
    COMPLETE_PROFILE: 100,
    FIRST_STACK_CREATED: 50,
    STACK_OPTIMIZED: 75,
    LEVEL_UP: 50,
    STACK_ADD: 10,
    STACK_REMOVE: 5,
    STACK_UPDATE: 5,
    STACK_BUILDER: 100,
  },
  STREAK_BONUS_MULTIPLIER: {
    7: 1.5, // 7-day streak: 1.5x bonus
    30: 2, // 30-day streak: 2x bonus
    100: 3, // 100-day streak: 3x bonus
  },
  // IMPORTANT: Gamification LEVELS have been re-ordered and ranges adjusted
  // to be mutually exclusive and sequential for correct level calculation.
  LEVELS: {
    BEGINNER: {
      min: 0,
      max: 499,
      title: 'Health Novice',
      perks: [
        'Basic scanning',
        'Limited stack size (5 items)',
        'Essential health tips',
      ],
    },
    EXPLORER: {
      min: 500,
      max: 999,
      title: 'Supplement Explorer',
      perks: [
        'Increased stack size (10 items)',
        'Basic AI chat access',
        'Unlock daily health challenges',
      ],
    },
    INTERMEDIATE: {
      min: 1000,
      max: 4999,
      title: 'Intermediate',
      perks: [
        'Access to advanced health insights',
        'Enhanced gamification rewards',
      ],
    },
    ANALYST: {
      min: 5000,
      max: 9999,
      title: 'Stack Analyst',
      perks: [
        'Expanded stack size (15 items)',
        'Advanced AI features (e.g., personalized insights)',
        'Access to community forums',
      ],
    },
    ADVANCED: {
      min: 10000,
      max: 24999,
      title: 'Advanced',
      perks: [
        'Priority access to new features',
        'Exclusive content and webinars',
      ],
    },
    EXPERT: {
      min: 25000,
      max: 49999,
      title: 'Health Expert',
      perks: [
        'Unlimited stack size',
        'Priority AI responses',
        'Early access to new features',
      ],
    },
    MASTER: {
      min: 50000,
      max: 99999,
      title: 'Pharma Master',
      perks: [
        'Special recognition badge',
        'Ability to contribute to product database',
        'Exclusive webinars',
      ],
    },
    GURU: {
      min: 100000,
      max: Infinity,
      title: 'Wellness Guru',
      perks: [
        'Top tier recognition',
        'Direct line to expert support',
        'Participate in content creation',
      ],
    },
  },
  // IMPORTANT: Achievement IDs and prerequisites updated to match their keys
  // and use GamificationAchievementId type for consistency.
  ACHIEVEMENTS: {
    FIRST_STEPS: {
      id: 'FIRST_STEPS',
      name: 'First Steps',
      description: 'Complete your first product scan',
      points: 50,
      icon: '🎯',
      category: 'scanning',
    },
    SAFETY_FIRST: {
      id: 'SAFETY_FIRST',
      name: 'Safety First',
      description: 'Identify your first interaction warning',
      points: 100,
      icon: '🛡️',
      category: 'safety',
      prerequisites: ['FIRST_STEPS'],
    },
    STACK_BUILDER: {
      id: 'STACK_BUILDER',
      name: 'Stack Builder',
      description: 'Add 5 items to your stack',
      points: 75,
      icon: '📚',
      category: 'stack_management',
      prerequisites: ['FIRST_STEPS'],
    },
    KNOWLEDGE_SEEKER: {
      id: 'KNOWLEDGE_SEEKER',
      name: 'Knowledge Seeker',
      description: 'Have 10 AI consultations',
      points: 100,
      icon: '🧠',
      category: 'ai_engagement',
    },
    CONTRIBUTOR: {
      id: 'CONTRIBUTOR',
      name: 'Community Contributor',
      description: 'Submit your first product for database expansion',
      points: 150,
      icon: '🤝',
      category: 'contribution',
    },
    STREAK_MASTER: {
      id: 'STREAK_MASTER',
      name: 'Streak Master',
      description: 'Maintain a 7-day daily scanning streak',
      points: 200,
      icon: '🔥',
      category: 'streak',
      prerequisites: ['FIRST_STEPS'],
    },
    OPTIMIZER: {
      id: 'OPTIMIZER',
      name: 'Stack Optimizer',
      description: 'Optimize your stack to reduce potential interactions',
      points: 120,
      icon: '✨',
      category: 'stack_management',
      prerequisites: ['STACK_BUILDER'],
    },
    SHARER: {
      id: 'SHARER',
      name: 'Knowledge Sharer',
      description: 'Share AI advice or your stack with others (3 times)',
      points: 80,
      icon: '🗣️',
      category: 'social',
    },
  },
};

export type ProductCategory = keyof typeof PRODUCT_CATEGORIES;

export const PRODUCT_CATEGORIES = {
  vitamin: { label: 'Vitamins', icon: '💊', color: COLORS.primary },
  mineral: { label: 'Minerals', icon: '⚗️', color: COLORS.secondary },
  herbal: { label: 'Herbals', icon: '🌿', color: COLORS.success },
  probiotic: { label: 'Probiotics', icon: '🦠', color: COLORS.info },
  omega3: { label: 'Omega-3s', icon: '🐟', color: COLORS.accent },
  protein: { label: 'Protein', icon: '💪', color: COLORS.warning },
  specialty: { label: 'Specialty', icon: '✨', color: COLORS.primaryLight },
  multivitamin: {
    label: 'Multivitamins',
    icon: '🌈',
    color: COLORS.secondaryDark,
  },
  amino_acid: { label: 'Amino Acids', icon: '🧬', color: COLORS.gray600 },
  enzyme: { label: 'Enzymes', icon: '🧪', color: COLORS.error },
};

export const QUALITY_INDICATORS = {
  THIRD_PARTY_TESTED: {
    label: 'Third-Party Tested',
    points: 15,
    icon: '✓',
    description:
      'Independently verified for purity and potency by external labs.',
  },
  GMP_CERTIFIED: {
    label: 'GMP Certified',
    points: 10,
    icon: '🏭',
    description:
      'Manufactured in a facility adhering to Good Manufacturing Practices (GMP).',
  },
  ORGANIC: {
    label: 'Organic',
    points: 8,
    icon: '🌱',
    description:
      'Certified USDA Organic, ensuring no synthetic pesticides or fertilizers.',
  },
  NON_GMO: {
    label: 'Non-GMO',
    points: 5,
    icon: '🚫',
    description:
      'Verified by the Non-GMO Project, free from genetically modified organisms.',
  },
  VEGAN: {
    label: 'Vegan',
    points: 3,
    icon: '🌿',
    description: 'Free from all animal-derived ingredients.',
  },
  GLUTEN_FREE: {
    label: 'Gluten-Free',
    points: 3,
    icon: '🌾',
    description:
      'Certified gluten-free for individuals with sensitivities or celiac disease.',
  },
  SOY_FREE: {
    label: 'Soy-Free',
    points: 3,
    icon: '🚫',
    description:
      'Free from soy, suitable for those with soy allergies or preferences.',
  },
};

export const ANIMATIONS = {
  DURATION: {
    fast: 200,
    normal: 300,
    slow: 500,
    verySlow: 800,
  },
  EASING: {
    easeIn: [0.4, 0, 1, 1],
    easeOut: [0, 0, 0.2, 1],
    easeInOut: [0.4, 0, 0.2, 1],
    spring: [0.175, 0.885, 0.32, 1.275],
    bounce: [0.34, 1.56, 0.64, 1],
  },
};

export const APP_CONFIG = {
  MAX_STACK_ITEMS_FREE: 10,
  MAX_STACK_ITEMS_PRO: 999,
  MAX_RECENT_SCANS: 20,
  CACHE_DURATION_HOURS: 24,
  CHAT_CACHE_DURATION_HOURS: 2,
  AI_RESPONSE_MAX_LENGTH: 500,
  SCAN_COOLDOWN_MS: 1000,
  API_TIMEOUT_MS: 30000,
  MIN_PASSWORD_LENGTH: 8,
  DEBOUNCE_TIME_MS: 300,
  ANONYMOUS_USER_PREFIX: 'anon_',
  HIPAA_LOCAL_ONLY: true,
  TIER_THRESHOLDS: {
    RULE_BASED_MAX_AGE_HOURS: 168,
    CACHE_MAX_AGE_HOURS: 24,
    API_FALLBACK_TIMEOUT_MS: 5000,
  },
  ACHIEVEMENT_THRESHOLDS: {
    FIRST_10_SCANS: 10,
    SCAN_MASTER: 100,
    SAFETY_GUARDIAN: 10,
    LEVEL_5: 5,
    LEVEL_10: 10,
    WEEK_WARRIOR: 7,
    MONTHLY_CHAMPION: 30,
    CENTURY_LEGEND: 100,
    STACK_BUILDER: 5,
    KNOWLEDGE_SEEKER: 3,
    SHARER: 5,
    CONTRIBUTOR: 5,
  },
  SEARCH_CACHE_DURATION_MS: 5 * 60 * 1000, // 5 minutes
  MAX_SEARCH_RESULTS: 20,
  USER_AGENT: 'PharmaGuide/1.0',
  DSLD_API_KEY: process.env.DSLD_API_KEY || '',
  USDA_API_KEY: process.env.USDA_FDC_API_KEY || '',
  FDA_API_KEY: process.env.OPENFDA_API_KEY || '',
  PUBMED_API_KEY: process.env.PUBMED_API_KEY || '',
};

export const ERROR_MESSAGES = {
  NETWORK_ERROR:
    'Network connection issue. Please check your internet and try again.',
  PRODUCT_NOT_FOUND:
    'Product not found. Try scanning again or enter details manually.',
  AI_UNAVAILABLE:
    'AI service is currently unavailable. Operating in rule-based or limited mode.',
  SCAN_ERROR:
    'Unable to process barcode. Please ensure good lighting and try again, or enter manually.',
  AUTH_ERROR:
    'Authentication failed. Please check your credentials and try again.',
  STACK_FULL:
    'Your stack has reached its limit. Upgrade to unlock unlimited items!',
  STACK_ERROR:
    'Unable to update your stack. Please try again.',
  INVALID_INPUT: 'Please ensure all required fields are filled correctly.',
  PASSWORD_MISMATCH: 'Passwords do not match. Please re-enter them.',
  PASSWORD_TOO_SHORT: `Password must be at least ${APP_CONFIG.MIN_PASSWORD_LENGTH} characters long.`,
  GENERIC_ERROR: 'An unexpected error occurred. Please try again later.',
};

export const NOTIFICATION_TYPES = {
  DAILY_REMINDER: {
    id: 'daily_reminder',
    title: 'Daily Supplement Reminder',
    body: 'Time to log your supplements!',
    category: 'reminders',
  },
  INTERACTION_ALERT: {
    id: 'interaction_alert',
    title: 'Interaction Alert!',
    body: 'Potential interaction found in your stack.',
    category: 'safety',
  },
  NEW_FEATURE: {
    id: 'new_feature',
    title: 'New Feature Available!',
    body: 'Check out our latest update.',
    category: 'updates',
  },
  LEVEL_UP: {
    id: 'level_up',
    title: 'Level Up! 🎉',
    body: "You've reached a new gamification level!",
    category: 'gamification',
  },
};

export const STORAGE_KEYS = {
  GAMIFICATION_PREFIX: '@pharmaguide_gamification:',
  PRODUCT_CACHE: 'product_cache',
  INTERACTION_CACHE: 'interaction_cache',
  ADVERSE_EVENT_CACHE: 'adverse_event_cache',
  EVIDENCE_CACHE: 'evidence_cache',
  RECENT_SEARCHES: '@pharmaguide_recent_searches',
};

export type VerificationLevel =
  | 'FDA_VERIFIED'
  | 'NIH_VALIDATED'
  | 'CLINICAL_STUDY'
  | 'RULE_BASED'
  | 'AI_ANALYSIS';
export type EvidenceLevel = keyof typeof EVIDENCE_LEVELS;
export type GamificationPoint = keyof typeof GAMIFICATION.POINTS;
export type GamificationLevel = keyof typeof GAMIFICATION.LEVELS;
export type GamificationAchievementId = keyof typeof GAMIFICATION.ACHIEVEMENTS;