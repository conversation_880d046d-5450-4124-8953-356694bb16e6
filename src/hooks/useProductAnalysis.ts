// src/hooks/useProductAnalysis.ts
import { useState, useEffect, useCallback } from 'react';
import { Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation } from '@react-navigation/native';
import { useStackStore } from '../stores/stackStore';
import { gamificationService } from '../services/gamification/gamificationService';
import { threeTierRouter } from '../services/ai/threeTierRouter';
import type { Product, ProductAnalysis } from '../types';
import { STORAGE_KEYS, EVIDENCE_LEVELS } from '../constants';

interface UseProductAnalysisProps {
  product: Product;
  analysis: ProductAnalysis;
}

interface UseProductAnalysisReturn {
  savedToStack: boolean;
  loading: boolean;
  error: string | null;
  handleAddToStack: () => Promise<void>;
  handleTalkToAI: () => void;
  determineEvidenceLevel: (analysis: ProductAnalysis) => 'A' | 'B' | 'C' | 'D';
}

export const useProductAnalysis = ({
  product,
  analysis,
}: UseProductAnalysisProps): UseProductAnalysisReturn => {
  const [savedToStack, setSavedToStack] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { addToStack, stack } = useStackStore();
  const navigation = useNavigation();

  useEffect(() => {
    if (product && analysis) {
      saveToRecentScans(product, analysis);
      updateGamification();
    }
  }, [product, analysis]);

  useEffect(() => {
    setLoading(!analysis);
  }, [analysis]);

  const determineEvidenceLevel = useCallback(
    (anal: ProductAnalysis): 'A' | 'B' | 'C' | 'D' => {
      if (!anal.stackInteraction || !anal.stackInteraction.interactions) {
        return 'D';
      }
      const hasClinical = anal.stackInteraction.interactions.some(i =>
        i.evidenceSources?.some(s => s.text.includes('Clinical'))
      );
      const hasCase = anal.stackInteraction.interactions.some(i =>
        i.evidenceSources?.some(s => s.text.includes('Case'))
      );
      if (hasClinical) return 'A';
      if (hasCase) return 'B';
      if (anal.stackInteraction.interactions.length > 0) return 'C';
      return 'D';
    },
    []
  );

  const saveToRecentScans = useCallback(
    async (prod: Product, anal: ProductAnalysis) => {
      try {
        const existingScans = await AsyncStorage.getItem(STORAGE_KEYS.RECENT_SCANS);
        const scans = existingScans ? JSON.parse(existingScans) : [];
        const evidenceLevel = determineEvidenceLevel(anal);
        const newScan = {
          id: Date.now().toString(),
          name: prod.name,
          brand: prod.brand,
          imageUrl: prod.imageUrl,
          score: anal.overallScore,
          hasInteraction: anal.stackInteraction
            ? anal.stackInteraction.overallRiskLevel !== 'NONE'
            : false,
          hasNutrientWarning: anal.nutrientWarnings?.length > 0,
          evidence: evidenceLevel,
          scannedAt: new Date().toISOString(),
        };
        const updatedScans = [newScan, ...scans].slice(0, 50);
        await AsyncStorage.setItem(STORAGE_KEYS.RECENT_SCANS, JSON.stringify(updatedScans));
        console.log('Scan saved to recent scans:', prod.name);
      } catch (error) {
        console.error('Error saving recent scan:', error);
      }
    },
    [determineEvidenceLevel]
  );

  const updateGamification = useCallback(async () => {
    try {
      await gamificationService.awardPoints('DAILY_SCAN');
      await gamificationService.updateStreak();
      if (analysis.overallScore >= 70) {
        await gamificationService.awardPoints('SAFE_PRODUCT');
      }
      if (
        analysis.stackInteraction &&
        analysis.stackInteraction.overallRiskLevel !== 'NONE'
      ) {
        await gamificationService.awardPoints('INTERACTION_FOUND');
      }
      if (analysis.nutrientWarnings?.length > 0) {
        await gamificationService.awardPoints('INTERACTION_FOUND');
      }
    } catch (error) {
      console.error('Error updating gamification:', error);
    }
  }, [analysis]);

  const handleAddToStack = useCallback(async () => {
    if (analysis.stackInteraction?.overallRiskLevel === 'CRITICAL' || analysis.nutrientWarnings?.some(w => w.percentOfLimit > 200)) {
      Alert.alert(
        'Critical Safety Warning',
        'This product has CRITICAL interactions or excessive nutrient levels. Adding it may pose a severe risk. Please consult your healthcare provider.',
        [{ text: 'OK' }]
      );
      return;
    }

    const generateUUID = () => {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {
        const r = (Math.random() * 16) | 0;
        const v = c === 'x' ? r : (r & 0x3) | 0x8;
        return v.toString(16);
      });
    };

    const itemToAdd = {
      item_id: generateUUID(),
      name: product.name,
      type: 'supplement' as 'medication' | 'supplement',
      dosage: product.dosage || 'As directed',
      frequency: 'Daily',
      ingredients: product.ingredients?.map(ing => ({
        name: ing.name,
        amount: ing.amount,
        unit: ing.unit,
      })) || [],
      brand: product.brand,
      imageUrl: product.imageUrl,
    };

    try {
      setLoading(true);
      setError(null); // Clear any previous errors
      await addToStack(itemToAdd);
      setSavedToStack(true);
      Alert.alert(
        'Added to Stack! 📚',
        `${product.name} has been saved to your supplement stack.`,
        [{ text: 'Great!', style: 'default' }]
      );
      await gamificationService.awardPoints('FIRST_STACK_CREATED');
    } catch (error) {
      console.error('Failed to add to stack:', error);
      setError('Could not add product to stack. Please try again.');
      Alert.alert('Error', 'Could not add product to stack. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [product, analysis, addToStack]);

  const handleTalkToAI = useCallback(async () => {
    setLoading(true);
    try {
      const aiResult = await threeTierRouter.analyzeProduct(product, stack, undefined, {
        priority: 'quality',
      });
      Alert.alert(
        'AI Pharmacist Ready! 🧠',
        `I'm ready to discuss ${product.name}. Ask about interactions, dosing, or health goals!`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Start Chat',
            onPress: () => {
              navigation.navigate('MainTabs', {
                screen: 'AI',
                params: {
                  productContext: {
                    name: product.name,
                    brand: product.brand,
                    analysis: { ...analysis, aiResult: aiResult.result },
                    initialMessage: `I'd like to discuss ${product.name}${product.brand ? ` by ${product.brand}` : ''}. Can you help me understand its safety and interactions?`,
                  },
                },
              });
            },
          },
        ]
      );
    } catch (error) {
      console.error('AI analysis error:', error);
      setError('Unable to connect to AI service. Try again later.');
      Alert.alert('Error', 'Unable to connect to AI service. Try again later.');
    } finally {
      setLoading(false);
    }
  }, [product, analysis, stack, navigation]);

  return {
    savedToStack,
    loading,
    error,
    handleAddToStack,
    handleTalkToAI,
    determineEvidenceLevel,
  };
};