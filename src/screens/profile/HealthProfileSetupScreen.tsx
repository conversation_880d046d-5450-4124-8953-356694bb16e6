// src/screens/profile/HealthProfileSetupScreen.tsx
// 🚀 WORLD-CLASS: Fast, Light, Legal, Easy, Sleek
import React, { useState, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Animated,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { ConsentModal } from '../../components/privacy/ConsentModal';
import { COLORS, SPACING, TYPOGRAPHY } from '../../constants';
import type { ConsentType } from '../../types/healthProfile';
import { HealthProfileSetupScreenProps } from '../../types/navigation';
import { useHealthProfile } from '../../hooks/useHealthProfile';
import { useAuth } from '../../hooks/useAuth';
import { useSetupProgress } from '../../hooks/useSetupProgress';

export const HealthProfileSetupScreen: React.FC<
  HealthProfileSetupScreenProps
> = ({ navigation, route }) => {
  const { user } = useAuth();
  const [showConsentModal, setShowConsentModal] = useState(false);
  const [progressAnim] = useState(new Animated.Value(0));

  // 🔒 HIPAA-compliant health profile management
  const {
    updateProfile,
    loading: profileLoading,
    completeness,
    refreshProfile,
  } = useHealthProfile();

  // 🎯 UNIFIED: Use the new unified progress manager
  const {
    progress,
    updateStep,
    updateFormData,
    updateConsents,
    loadProgress,
    saveProgress,
    clearProgress,
    isLoading: progressLoading,
  } = useSetupProgress();

  const { currentStep, steps, data: setupData, consents } = progress;

  // Refresh health profile and completeness on focus
  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      refreshProfile();
      loadProgress(); // Reload progress when returning to screen
    });
    return unsubscribe;
  }, [navigation, refreshProfile, loadProgress]);

  // Calculate local progress based on actual step completion
  const localProgress = useMemo(() => {
    const completedSteps = steps.filter(step => step.completed).length;
    return Math.round((completedSteps / steps.length) * 100);
  }, [steps]);

  // ✨ SLEEK: Animate progress based on actual step completion
  useEffect(() => {
    const completedSteps = steps.filter(step => step.completed).length;
    const actualProgress = (completedSteps / steps.length) * 100;
    console.log(
      `📊 Actual progress: ${completedSteps}/${steps.length} = ${actualProgress}%`
    );

    Animated.timing(progressAnim, {
      toValue: actualProgress / 100,
      duration: 500,
      useNativeDriver: false,
    }).start();
  }, [steps, progressAnim]);

  // ⚖️ LEGAL: Proper consent handling
  const handleConsentGranted = async (
    grantedConsents: Record<ConsentType, boolean>
  ) => {
    try {
      await updateConsents(grantedConsents);
      setShowConsentModal(false);

      // Mark privacy_consent step as completed WITH data
      const privacyData = {
        consents: grantedConsents,
        timestamp: new Date().toISOString(),
      };
      await updateStep('privacy_consent', true, privacyData);

      console.log('✅ Privacy consent granted and saved');
    } catch (error) {
      console.error('Consent error:', error);
    }
  };

  // 🎨 EASY: Simple navigation
  const handleStepPress = (stepIndex: number) => {
    const step = steps[stepIndex];

    if (stepIndex === 0) {
      setShowConsentModal(true);
      return;
    }

    if (stepIndex > currentStep + 1) return;

    navigateToStep(step.id);
  };

  const navigateToStep = (stepId: string) => {
    // 🧭 SMART: Navigation mapping
    const screenMap: Record<string, string> = {
      demographics: 'DemographicsScreen',
      health_goals: 'HealthGoalsScreen',
      health_conditions: 'HealthConditionsScreen',
      allergies: 'AllergiesScreen',
    };

    const screenName = screenMap[stepId];
    if (screenName) {
      // Pass saved data for this step as initialValue prop
      navigation.navigate(screenName as any, {
        initialValue: setupData[stepId] || null,
        fromSetup: true,
      });
    }
  };

  // 🎉 Handle setup completion with success message
  const handleCompleteSetup = async () => {
    try {
      // 🔒 CRITICAL FIX: Save all health profile data to secure storage
      console.log('💾 Saving health profile data...', setupData);

      // Save each section of the health profile
      const sections = [
        'demographics',
        'goals',
        'conditions',
        'allergies',
        'privacy',
      ];

      let savedSections = 0;
      for (const section of sections) {
        if (setupData[section]) {
          console.log(`💾 Saving ${section} data:`, setupData[section]);
          const result = await updateProfile(
            section as any,
            setupData[section]
          );
          if (result.error) {
            console.error(`❌ Error saving ${section}:`, result.error);
            throw new Error(`Failed to save ${section} data: ${result.error}`);
          }
          console.log(`✅ Successfully saved ${section} data`);
          savedSections++;
        } else {
          console.log(`ℹ️ No data for ${section}, skipping...`);
        }
      }

      console.log(
        `✅ Setup completion: Saved ${savedSections} sections to secure storage`
      );

      // Refresh the profile to ensure UI is updated
      await refreshProfile();

      // Clear saved progress since setup is complete
      await clearProgress();

      // Show success popup with Stack tab guidance and View/Edit Profile option
      Alert.alert(
        '🎉 Profile Setup Complete!',
        `Great job! Your health profile is now set up and saved securely (${savedSections} sections saved).\n\nNext step: Optimize your stack by adding supplements or medications in the Stack tab to check for interactions.`,
        [
          {
            text: 'View/Edit Profile',
            onPress: () =>
              navigation.navigate('MainTabs', { screen: 'Profile' }),
          },
          {
            text: 'Return to Home',
            onPress: () => navigation.navigate('MainTabs', { screen: 'Home' }),
            style: 'cancel',
          },
        ]
      );
    } catch (error) {
      console.error('❌ Error completing setup:', error);
      Alert.alert(
        'Save Error',
        'There was an issue saving your health profile. Please try again.',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    }
  };

  // 💾 Handle save progress and return to ProfileScreen
  const handleSaveProgress = async () => {
    try {
      await saveProgress();
      Alert.alert(
        '💾 Progress Saved!',
        'Your progress has been saved. You can continue setup anytime from the Profile tab.',
        [
          {
            text: 'Continue Setup',
            style: 'cancel',
          },
          {
            text: 'Return to Profile',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error) {
      console.error('Error saving progress:', error);
      Alert.alert('Error', 'Failed to save progress. Please try again.');
    }
  };

  // Show loading state
  if (progressLoading || profileLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading your progress...</Text>
        </View>
      </SafeAreaView>
    );
  }

  const renderStepItem = (step: any, index: number) => {
    const isCompleted = step.completed;
    const isCurrent = index === currentStep;
    const isAccessible = index <= currentStep + 1;

    return (
      <TouchableOpacity
        key={step.id}
        style={[
          styles.stepItem,
          isCompleted && styles.stepCompleted,
          isCurrent && styles.stepCurrent,
          !isAccessible && styles.stepDisabled,
        ]}
        onPress={() => handleStepPress(index)}
        disabled={!isAccessible}
        accessibilityRole="button"
        accessibilityLabel={`${step.title} step ${isCompleted ? 'completed' : 'not completed'}`}
      >
        <View style={styles.stepHeader}>
          <View style={styles.stepNumberContainer}>
            {isCompleted ? (
              <Ionicons name="checkmark-circle" size={24} color={COLORS.success} />
            ) : (
              <Text style={[styles.stepNumber, isCurrent && styles.stepNumberCurrent]}>
                {index + 1}
              </Text>
            )}
          </View>
          <View style={styles.stepContent}>
            <Text style={[styles.stepTitle, isCompleted && styles.stepTitleCompleted]}>
              {step.title}
            </Text>
            <Text style={[styles.stepDescription, isCompleted && styles.stepDescriptionCompleted]}>
              {step.description}
            </Text>
            {step.estimatedTime && (
              <Text style={styles.stepTime}>~{step.estimatedTime} min</Text>
            )}
          </View>
          {isAccessible && (
            <Ionicons
              name="chevron-forward"
              size={20}
              color={isCurrent ? COLORS.primary : COLORS.textSecondary}
            />
          )}
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Minimal Header */}
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={styles.backButton}
          accessibilityRole="button"
          accessibilityLabel="Go back"
        >
          <Ionicons name="arrow-back" size={24} color={COLORS.textPrimary} />
        </TouchableOpacity>
        <Text
          style={styles.title}
          accessibilityRole="header"
          accessibilityLabel="Health Profile Setup"
        >
          Health Profile
        </Text>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          accessibilityRole="button"
          accessibilityLabel="Skip setup and return"
        >
          <Text style={styles.skipText}>Skip</Text>
        </TouchableOpacity>
      </View>

      {/* Animated Progress */}
      <View style={styles.progressContainer}>
        <View style={styles.progressBar}>
          <Animated.View
            style={[
              styles.progressFill,
              {
                width: progressAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: ['0%', '100%'],
                }),
              },
            ]}
          />
        </View>
        <Text style={styles.progressText}>{localProgress}% Complete</Text>
      </View>

      {/* Steps List */}
      <ScrollView style={styles.stepsContainer} showsVerticalScrollIndicator={false}>
        <Text style={styles.sectionTitle}>Setup Steps</Text>
        {steps.map(renderStepItem)}
      </ScrollView>

      {/* Footer Actions */}
      <View style={styles.footer}>
        {completeness === 100 ? (
          <TouchableOpacity
            style={styles.completeButton}
            onPress={handleCompleteSetup}
            accessibilityRole="button"
            accessibilityLabel="Complete profile setup"
          >
            <Text style={styles.completeButtonText}>Complete Setup</Text>
            <Ionicons name="arrow-forward" size={20} color={COLORS.white} />
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            style={styles.saveProgressButton}
            onPress={handleSaveProgress}
            accessibilityRole="button"
            accessibilityLabel="Save progress and return later"
          >
            <Ionicons
              name="bookmark-outline"
              size={20}
              color={COLORS.primary}
            />
            <Text style={styles.saveProgressButtonText}>Save Progress</Text>
          </TouchableOpacity>
        )}
      </View>

      <ConsentModal
        visible={showConsentModal}
        onClose={() => setShowConsentModal(false)}
        onConsentsGranted={handleConsentGranted}
        title="Privacy Settings"
      />
    </SafeAreaView>
  );
};

// ✨ SLEEK: Modern, minimal styles
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
  },
  backButton: {
    padding: SPACING.xs,
  },
  title: {
    fontSize: TYPOGRAPHY.sizes.lg,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
  },
  skipText: {
    fontSize: TYPOGRAPHY.sizes.base,
    color: COLORS.primary,
    fontWeight: TYPOGRAPHY.weights.medium,
  },
  progressContainer: {
    paddingHorizontal: SPACING.lg,
    paddingBottom: SPACING.lg,
  },
  progressBar: {
    height: 4,
    backgroundColor: COLORS.gray200,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: COLORS.primary,
    borderRadius: 2,
  },
  progressText: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginTop: SPACING.xs,
  },
  content: {
    flex: 1,
    paddingHorizontal: SPACING.lg,
  },
  hero: {
    alignItems: 'center',
    paddingVertical: SPACING.xl,
  },
  heroTitle: {
    fontSize: TYPOGRAPHY.sizes.xl,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: COLORS.textPrimary,
    marginTop: SPACING.sm,
  },
  heroSubtitle: {
    fontSize: TYPOGRAPHY.sizes.base,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginTop: SPACING.xs,
    lineHeight: 22,
  },
  stepsContainer: {
    marginBottom: SPACING.xl,
  },
  stepItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.backgroundSecondary,
    borderRadius: 12,
    padding: SPACING.md,
    marginBottom: SPACING.sm,
  },
  stepCompleted: {
    backgroundColor: COLORS.successLight,
  },
  stepCurrent: {
    backgroundColor: COLORS.primaryLight,
    borderWidth: 1,
    borderColor: COLORS.primary,
  },
  stepDisabled: {
    backgroundColor: COLORS.gray300,
  },
  stepHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: SPACING.xs,
  },
  stepNumberContainer: {
    marginRight: SPACING.md,
  },
  stepNumber: {
    fontSize: TYPOGRAPHY.sizes.sm,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: COLORS.white,
  },
  stepNumberCurrent: {
    color: COLORS.primary,
  },
  stepContent: {
    flex: 1,
  },
  stepTitle: {
    fontSize: TYPOGRAPHY.sizes.base,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
  },
  stepTitleCompleted: {
    color: COLORS.success,
  },
  stepDescription: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
    lineHeight: 18,
  },
  stepDescriptionCompleted: {
    color: COLORS.success,
  },
  stepTime: {
    fontSize: TYPOGRAPHY.sizes.xs,
    color: COLORS.textSecondary,
  },
  footer: {
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.lg,
  },
  completeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.primary,
    paddingVertical: SPACING.md,
    borderRadius: 12,
    gap: SPACING.sm,
  },
  completeButtonText: {
    fontSize: TYPOGRAPHY.sizes.base,
    color: COLORS.white,
    fontWeight: TYPOGRAPHY.weights.semibold,
  },
  saveProgressButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.backgroundSecondary,
    paddingVertical: SPACING.md,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: COLORS.primary,
    gap: SPACING.sm,
  },
  saveProgressButtonText: {
    fontSize: TYPOGRAPHY.sizes.base,
    color: COLORS.primary,
    fontWeight: TYPOGRAPHY.weights.semibold,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: TYPOGRAPHY.sizes.base,
    color: COLORS.textSecondary,
  },
  sectionTitle: {
    fontSize: TYPOGRAPHY.sizes.base,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.sm,
  },
});
