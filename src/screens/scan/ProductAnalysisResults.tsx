// src/screens/scan/ProductAnalysisResults.tsx
import React from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  Text,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { AnimatedTouchable } from '../../components/common';
import { ProductInfoCard } from '../../components/scan/ProductInfoCard';
import { ScoreDisplay } from '../../components/scan/ScoreDisplay';
import { InteractionAlert } from '../../components/scan/InteractionAlert';
import { ActionButtons } from '../../components/scan/ActionButtons';
import { useProductAnalysis } from '../../hooks/useProductAnalysis';
import type { Product, ProductAnalysis } from '../../types';
import { COLORS, SPACING, TYPOGRAPHY, EVIDENCE_LEVELS, ERROR_MESSAGES } from '../../constants/index'; // Import ERROR_MESSAGES
import { gamificationService } from '../../services/gamification/gamificationService';

interface ProductAnalysisResultsProps {
  product?: Product;
  analysis?: ProductAnalysis;
  onClose?: () => void;
  onScanAnother?: () => void;
}

export const ProductAnalysisResults: React.FC<ProductAnalysisResultsProps> = ({
  product: propProduct,
  analysis: propAnalysis,
  onClose: propOnClose,
  onScanAnother: propOnScanAnother,
}) => {
  const navigation = useNavigation();
  const route = useRoute();
  const routeParams = route.params as any;

  // Get data from props (ScanScreen) or route params (Navigation)
  const product = propProduct || routeParams?.product;
  const analysis = propAnalysis || routeParams?.analysis;
  const onClose =
    propOnClose || routeParams?.onClose || (() => navigation.goBack());
  const onScanAnother =
    propOnScanAnother ||
    routeParams?.onScanAnother ||
    (() => navigation.goBack());

  // Local loading state for awarding points
  const [isAwardingPoints, setIsAwardingPoints] = React.useState(false);

  // Use custom hook for business logic. Note: No PHI is sent to Supabase.
  // All user profile/health profile data is local-only.
  const { savedToStack, loading, error, handleAddToStack, handleTalkToAI, determineEvidenceLevel } =
    useProductAnalysis({ product: product!, analysis: analysis! });

  // Effect for critical safety warnings and gamification
  React.useEffect(() => {
    if (
      analysis?.stackInteraction?.overallRiskLevel === 'CRITICAL' ||
      analysis?.nutrientWarnings?.some(w => w.percentOfLimit > 200)
    ) {
      setIsAwardingPoints(true); // Start awarding points loading state
      Alert.alert(
        'Critical Safety Warning',
        'This product has critical safety concerns or excessive nutrient levels. Consult a healthcare provider before use.',
        [{ text: 'OK', onPress: () => {} }]
      );
      // Award points for identifying a safety concern
      gamificationService.awardPoints('SAFETY_FIRST')
        .catch(err => {
          console.error('Failed to award points for safety alert:', err);
          // Optionally, handle this error (e.g., local logging, user feedback)
        })
        .finally(() => setIsAwardingPoints(false)); // End awarding points loading state
    }
  }, [analysis]); // Re-run effect if analysis changes

  // Handle errors from useProductAnalysis hook
  if (error) {
    return (
      <ScrollView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.errorText}>{ERROR_MESSAGES.GENERIC_ERROR}</Text>
        </View>
      </ScrollView>
    );
  }

  // Show loading if no product/analysis data initially or if an async operation is in progress
  if (!product || !analysis) {
    return (
      <ScrollView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={COLORS.primary} />
          <Text style={styles.loadingText}>Loading product analysis...</Text>
        </View>
      </ScrollView>
    );
  }

  return (
    <ScrollView style={styles.container}>
      {loading || isAwardingPoints ? ( // Combine loading states
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={COLORS.primary} />
          <Text style={styles.loadingText}>
            {isAwardingPoints ? 'Processing safety rewards...' : 'Analyzing product interactions...'}
          </Text>
        </View>
      ) : (
        <>
          {/* Header */}
          <View style={styles.header}>
            <AnimatedTouchable
              onPress={onClose}
              style={styles.headerButton}
              accessibilityLabel="Close product analysis" // Accessibility
              accessibilityRole="button" // Accessibility
            >
              <Ionicons name="close" size={24} color={COLORS.textPrimary} />
            </AnimatedTouchable>
            <Text style={styles.headerTitle}>{product.name}</Text>
            <AnimatedTouchable
              onPress={handleAddToStack}
              style={styles.headerButton}
              accessibilityLabel={savedToStack ? "Remove from stack" : "Add to stack"} // Accessibility
              accessibilityRole="button" // Accessibility
            >
              <Ionicons
                name={savedToStack ? 'bookmark' : 'bookmark-outline'}
                size={24}
                color={savedToStack ? COLORS.primary : COLORS.textPrimary}
              />
            </AnimatedTouchable>
          </View>

          {/* Content */}
          <View style={styles.content}>
            <ProductInfoCard product={product} />
            <ScoreDisplay analysis={analysis} />
            {analysis.stackInteraction && (
              <InteractionAlert stackInteraction={analysis.stackInteraction} />
            )}

            {/* Safety Profile Section */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Safety Profile</Text>
              {analysis.safetyProfile.warnings.map((warning, index) => (
                <Text key={`safety-warning-${index}`} style={styles.warningText}>{warning}</Text>
              ))}
              {analysis.safetyProfile.contraindications.map((contra, index) => (
                <Text key={`safety-contra-${index}`} style={styles.contraText}>Contraindication: {contra}</Text>
              ))}
              {analysis.safetyProfile.interactions.map((int, index) => (
                <Text key={`safety-interaction-${index}`} style={styles.interactionText}> {/* Specific key */}
                  Interaction: {int.description} ({int.severity}, {int.source})
                </Text>
              ))}
            </View>

            {/* Nutrient Analysis Section */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Nutrient Analysis</Text>
              {analysis.ingredientAnalyses.map((ing, index) => (
                <Text key={`nutrient-analysis-${index}`} style={styles.recommendationText}> {/* Specific key */}
                  {ing.ingredient}: {ing.dosageAnalysis.providedAmount} {ing.dosageAnalysis.unit} (
                  {ing.dosageAnalysis.percentRDI?.toFixed(1)}% of RDI)
                </Text>
              ))}
              {analysis.nutrientWarnings?.map((warning, index) => (
                <Text key={`nutrient-warning-${index}`} style={styles.warningText}> {/* Specific key */}
                  Warning: {warning.nutrient} exceeds {warning.percentOfLimit}% of safe limit
                </Text>
              ))}
            </View>

            {/* Evidence Level Section */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Evidence Level</Text>
              <Text style={styles.recommendationText}>
                {/* Ensure determineEvidenceLevel returns a valid key for EVIDENCE_LEVELS */}
                {EVIDENCE_LEVELS[determineEvidenceLevel(analysis)]?.label}: {EVIDENCE_LEVELS[determineEvidenceLevel(analysis)]?.description}
              </Text>
            </View>

            <ActionButtons
              savedToStack={savedToStack}
              onScanAnother={onScanAnother}
              onAddToStack={handleAddToStack}
              onTalkToAI={handleTalkToAI}
            />
          </View>
        </>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray200,
  },
  headerTitle: {
    fontSize: TYPOGRAPHY.sizes.lg,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: COLORS.textPrimary,
  },
  headerButton: {
    padding: SPACING.sm,
    borderRadius: SPACING.xs,
  },
  content: {
    flexGrow: 1,
    paddingHorizontal: SPACING.lg,
    paddingBottom: SPACING.xl * 2,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: SPACING.xxl,
    minHeight: 300,
  },
  loadingText: {
    marginTop: SPACING.md,
    fontSize: TYPOGRAPHY.sizes.base,
    color: COLORS.textSecondary,
  },
  errorText: { // New style for error messages
    fontSize: TYPOGRAPHY.sizes.base,
    color: COLORS.error,
    textAlign: 'center',
  },
  section: {
    marginBottom: SPACING.lg,
  },
  sectionTitle: {
    fontSize: TYPOGRAPHY.sizes.lg,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.sm,
  },
  warningText: {
    fontSize: TYPOGRAPHY.sizes.base,
    color: COLORS.error,
    marginBottom: SPACING.xs,
  },
  contraText: {
    fontSize: TYPOGRAPHY.sizes.base,
    color: COLORS.warning,
    marginBottom: SPACING.xs,
  },
  interactionText: {
    fontSize: TYPOGRAPHY.sizes.base,
    color: COLORS.warning,
    marginBottom: SPACING.xs,
  },
  recommendationText: {
    fontSize: TYPOGRAPHY.sizes.base,
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
  },
});