// src/services/analysis/enhancedIngredientAnalyzer.ts
// Enhanced Ingredient-Level Analysis System

import axios from 'axios';
import { AICache } from '../ai/AICache';
import { rxNormService } from '../normalization/rxNormService';
import { cacheManager } from '../dataIntegration/cacheManager';
import { NutrientLimit } from '../../constants';
import { EVIDENCE_LEVELS } from '../../constants/index';
import type {
  UserProfile,
  EnhancedIngredientAnalysis,
  ClinicalUse,
  Warning,
  Recommendation,
  IngredientInteraction,
  Contraindication,
  SideEffect,
  SpecialPopulationWarning,
  AbsorptionFactor,
  FormAlternative
} from '../../types';

// Internal IngredientProfile interface for enhanced analysis
export interface IngredientProfile {
  name: string;
  category:
    | 'vitamin'
    | 'mineral'
    | 'herb'
    | 'amino_acid'
    | 'fatty_acid'
    | 'probiotic'
    | 'enzyme'
    | 'other';
  commonNames: string[];

  recommendedDailyIntake?: {
    adult: { min: number; max: number; unit: string };
    elderly?: { min: number; max: number; unit: string };
    pregnant?: { min: number; max: number; unit: string };
    breastfeeding?: { min: number; max: number; unit: string };
  };
  upperLimit?: {
    adult: { amount: number; unit: string };
    elderly?: { amount: number; unit: string };
    pregnant?: { amount: number; unit: string };
    breastfeeding?: { amount: number; unit: string };
  };

  bioavailability: {
    form: string;
    absorptionRate: number; // 0-100%
    bestTakenWith?: string[];
    avoidWith?: string[];
  };

  safety: {
    pregnancyCategory: 'A' | 'B' | 'C' | 'D' | 'X' | 'Unknown';
    breastfeedingCategory: 'Safe' | 'Caution' | 'Avoid' | 'Unknown';
    contraindications: string[];
    sideEffects: string[];
    drugInteractions: string[];
  };

  evidence: {
    level: 'A' | 'B' | 'C' | 'D';
    studyCount: number;
    benefits: string[];
    conditions: string[];
  };

  quality: {
    purityConcerns?: string[];
    contaminants?: string[];
    certifications?: string[];
    storageRequirements?: string[];
  };
}

export interface DemographicFactors {
  ageRange: string;
  biologicalSex: 'male' | 'female' | 'other';
  pregnancyStatus?: 'pregnant' | 'breastfeeding' | 'not_pregnant';
  healthConditions: string[];
  medications: string[];
}

export class EnhancedIngredientAnalyzer {
  private ingredientDatabase: Map<string, IngredientProfile> = new Map();
  private cache: AICache;

  constructor() {
    this.cache = new AICache();
    this.initializeDatabase();
  }

  /**
   * Main method to analyze a single ingredient with demographic considerations
   */
  async analyzeIngredient(
    ingredientName: string,
    amount: number,
    unit: string,
    userProfile?: UserProfile
  ): Promise<EnhancedIngredientAnalysis> {
    // Input validation
    if (!ingredientName || typeof amount !== 'number' || amount < 0 || !unit) {
      throw new Error('Invalid input: ingredient name, amount (non-negative), and unit are required.');
    }

    // Step 1: Normalize ingredient name
    const normalizedIngredientName = await this.normalizeIngredientName(ingredientName);

    const cacheKey = `${normalizedIngredientName}_${amount}_${unit}_${JSON.stringify(userProfile || {})}`;
    const cachedResult = await this.cache.get(cacheKey);
    if (cachedResult) {
      return cachedResult as EnhancedIngredientAnalysis;
    }

    // Step 2: Get internal ingredient profile
    const profile = this.getIngredientProfile(normalizedIngredientName);

    if (!profile) {
      const unknownAnalysis = this.generateUnknownIngredientAnalysis(
        ingredientName,
        amount,
        unit,
        userProfile
      );
      await this.cache.set(cacheKey, unknownAnalysis, 24 * 60 * 60);
      return unknownAnalysis;
    }

    const demographics: DemographicFactors = {
      ageRange: userProfile?.demographics?.ageRange || 'adult',
      biologicalSex: userProfile?.demographics?.biologicalSex || 'other',
      pregnancyStatus: userProfile?.demographics?.pregnancyStatus,
      healthConditions: userProfile?.conditions || [],
      medications: userProfile?.medications || [],
    };

    // Step 3: Perform comprehensive analysis
    const normalizedAmount = this.normalizeAmount(amount, unit, profile);
    const upperLimitAmount = this.getUpperLimit(profile, demographics)?.amount;
    const rdiRange = this.getTargetDosageRange(profile, demographics);

    const dosageScore = this.calculateDosageScore(normalizedAmount, profile, demographics);
    const bioavailabilityScore = this.calculateBioavailabilityScore(profile);
    const safetyScore = this.calculateSafetyScore(profile, demographics);
    const qualityScore = this.calculateQualityScore(profile);
    const evidenceScore = this.calculateEvidenceScore(profile);
    const overallScore = this.calculateOverallScore({
      dosageScore,
      bioavailabilityScore,
      safetyScore,
      qualityScore,
      evidenceScore,
    });

    // Build analysis components
    const dosageAnalysis = {
      providedAmount: amount,
      unit: unit,
      rdi: rdiRange ? rdiRange.min : undefined,
      rdiUnit: rdiRange ? rdiRange.unit : undefined,
      percentRDI: rdiRange ? (normalizedAmount / rdiRange.min) * 100 : undefined,
      therapeuticRange: rdiRange ? { min: rdiRange.min, max: rdiRange.max, unit: rdiRange.unit } : undefined,
      dosageAssessment: this.getDosageAssessment(normalizedAmount, rdiRange, upperLimitAmount) as 'below' | 'optimal' | 'high' | 'excessive',
      demographicAdjustment: (demographics.ageRange?.includes('65+') && rdiRange?.elderly)
        ? `Adjusted for elderly, min: ${rdiRange.elderly.min} ${rdiRange.elderly.unit}` : undefined,
    };

    const absorptionFactors: AbsorptionFactor[] = [];
    if (profile.bioavailability.bestTakenWith) {
      absorptionFactors.push(...profile.bioavailability.bestTakenWith.map(f => ({
        factor: f,
        effect: 'enhance' as const,
        magnitude: 'significant',
        timing: 'with meals'
      })));
    }
    if (profile.bioavailability.avoidWith) {
      absorptionFactors.push(...profile.bioavailability.avoidWith.map(f => ({
        factor: f,
        effect: 'inhibit' as const,
        magnitude: 'significant',
        timing: 'separate by 2+ hours'
      })));
    }

    const formAnalysis = {
      currentForm: profile.bioavailability.form,
      bioavailabilityRating: this.getBioavailabilityRating(profile.bioavailability.absorptionRate) as 'poor' | 'fair' | 'good' | 'excellent',
      alternativeForms: this.getAlternativeForms(profile),
      absorptionFactors,
    };

    const clinicalEvidence = {
      evidenceLevel: profile.evidence.level,
      studyCount: profile.evidence.studyCount,
      metaAnalysisCount: Math.floor(profile.evidence.studyCount / 10),
      primaryUses: profile.evidence.benefits.map(b => ({
        condition: b,
        evidenceCount: Math.floor(profile.evidence.studyCount / profile.evidence.benefits.length),
        strength: this.getEvidenceStrength(profile.evidence.level) as 'strong' | 'moderate' | 'preliminary'
      } as ClinicalUse)),
      efficacyByCondition: new Map(profile.evidence.conditions.map(c => [c, this.calculateEfficacyForCondition(profile.evidence.level)])),
      keyFindings: profile.evidence.benefits.slice(0, 3),
    };

    const qualityIndicators = {
      sourcingQuality: this.getSourceQuality(profile) as 'unknown' | 'standard' | 'premium' | 'pharmaceutical',
      manufacturingStandards: profile.quality.certifications || [],
      contaminantRisk: profile.quality.contaminants && profile.quality.contaminants.length > 0 ? 'high' as const : 
                      (profile.quality.purityConcerns && profile.quality.purityConcerns.length > 0 ? 'medium' as const : 'low' as const),
      shelfStability: this.getShelfStability(profile) as 'poor' | 'fair' | 'good' | 'excellent',
    };

    const warnings = this.generateWarnings(profile, normalizedAmount, demographics);
    const recommendations = this.generateRecommendations(profile, normalizedAmount, demographics);
    const ingredientInteractions = this.generateIngredientInteractions(profile, demographics);
    const contraindications = this.generateContraindications(profile, demographics);
    const sideEffects = this.generateSideEffects(profile);
    const specialPopulationWarnings = this.generateSpecialPopulationWarnings(profile, demographics);

    const safetyAnalysis = {
      upperLimit: upperLimitAmount,
      upperLimitUnit: this.getUpperLimit(profile, demographics)?.unit,
      percentOfUpperLimit: upperLimitAmount ? (normalizedAmount / upperLimitAmount) * 100 : undefined,
      contraindications,
      interactions: ingredientInteractions,
      sideEffects,
      pregnancyCategory: profile.safety.pregnancyCategory,
      specialPopulations: specialPopulationWarnings,
    };

    const analysisResult: EnhancedIngredientAnalysis = {
      ingredient: ingredientName,
      overallScore,
      scores: {
        dosage: dosageScore,
        bioavailability: bioavailabilityScore,
        safety: safetyScore,
        quality: qualityScore,
        evidence: evidenceScore,
        purity: qualityScore,
      },
      formAnalysis,
      dosageAnalysis,
      safetyAnalysis,
      clinicalEvidence,
      qualityIndicators,
      recommendations,
      warnings,
      dataSources: ['Internal Database', ...(profile.evidence.level !== 'D' ? ['PubMed'] : [])],
    };

    await this.cache.set(cacheKey, analysisResult, 24 * 60 * 60);
    return analysisResult;
  }

  /**
   * Normalizes ingredient names using RxNorm and local synonyms
   */
  private async normalizeIngredientName(name: string): Promise<string> {
    const normalized = name.toLowerCase().replace(/\s+/g, ' ').trim()
      .replace(/ hcl$| hydrochloride$| citrate$| malate$| glycinate$| gluconate$| sulfate$/, '');

    // Attempt RxNorm normalization first
    try {
      const rxNormConcept = await rxNormService.normalizeIngredientName(normalized);
      if (rxNormConcept?.name) {
        // Successfully normalized ingredient name via RxNorm
        return rxNormConcept.name;
      }
    } catch (rxError) {
      // RxNorm normalization failed, falling back to local synonyms
    }

    // Fallback to local synonym map
    const synonymMap: Record<string, string> = {
      'vitamin b12': 'cobalamin', 'cobalamin': 'cobalamin', 'vitamin b9': 'folate',
      'folic acid': 'folate', 'vitamin b3': 'niacin', 'nicotinic acid': 'niacin',
      'vitamin b5': 'pantothenic acid', 'vitamin b6': 'pyridoxine', 'vitamin b1': 'thiamine',
      'vitamin b2': 'riboflavin', 'fish oil': 'omega-3 fatty acids', 'omega-3': 'omega-3 fatty acids',
      'coq10': 'coenzyme q10', 'co-q10': 'coenzyme q10', 'sam-e': 's-adenosyl methionine',
      'magnesium citrate': 'magnesium', 'magnesium oxide': 'magnesium', 'calcium carbonate': 'calcium',
      'calcium citrate': 'calcium', 'curcumin': 'turmeric', 'chondroitin sulfate': 'chondroitin',
      'glucosamine hcl': 'glucosamine', 'l-theanine': 'theanine', 'alpha-gpc': 'choline',
      'acetyl-l-carnitine': 'carnitine', '5-htp': '5-hydroxytryptophan', 'n-acetyl cysteine': 'n-acetylcysteine',
    };
    
    const mappedName = synonymMap[normalized];
    if (mappedName) {
      // Successfully normalized via synonym map
      return mappedName;
    }

    return normalized;
  }

  /**
   * Generates analysis for unknown ingredients with appropriate warnings
   */
  private generateUnknownIngredientAnalysis(
    name: string,
    amount: number,
    unit: string,
    userProfile?: UserProfile
  ): EnhancedIngredientAnalysis {
    const defaultWarning: Warning = {
      severity: 'critical',
      type: 'safety',
      message: `Unknown ingredient: ${name}. Cannot assess safety.`,
      recommendation: 'Consult healthcare provider before use and research ingredient thoroughly.',
    };
    
    const defaultRecommendation: Recommendation = {
      type: 'dosage',
      priority: 'high',
      title: 'Unknown Ingredient Safety',
      description: 'This ingredient is not in our database',
      impact: 'Research thoroughly before use',
    };

    return {
      ingredient: name,
      overallScore: 30,
      scores: { safety: 30, evidence: 10, dosage: 50, bioavailability: 50, quality: 30, purity: 30 },
      formAnalysis: { 
        currentForm: 'unknown', 
        bioavailabilityRating: 'poor', 
        alternativeForms: [], 
        absorptionFactors: [] 
      },
      dosageAnalysis: { 
        providedAmount: amount, 
        unit: unit, 
        dosageAssessment: 'below',
      },
      safetyAnalysis: {
        contraindications: userProfile?.conditions && userProfile.conditions.length > 0 ? 
          [{ condition: 'Unknown interactions', severity: 'absolute', reason: 'Cannot assess safety', source: 'Unknown' }] : [],
        interactions: [],
        sideEffects: [],
        specialPopulations: [],
      },
      clinicalEvidence: { 
        evidenceLevel: 'F', 
        studyCount: 0, 
        metaAnalysisCount: 0, 
        primaryUses: [], 
        efficacyByCondition: new Map(),
        keyFindings: [],
      },
      qualityIndicators: { 
        sourcingQuality: 'unknown', 
        manufacturingStandards: [], 
        contaminantRisk: 'high',
        shelfStability: 'poor',
      },
      warnings: [defaultWarning],
      recommendations: [defaultRecommendation],
      dataSources: ['Internal Database (Unknown)', 'AI Analysis (Fallback)'],
    };
  }

  // Helper methods for enhanced analysis
  private getDosageAssessment(
    normalizedAmount: number,
    rdiRange: { min: number; max: number; unit: string } | null,
    upperLimit?: number
  ): string {
    if (!rdiRange && !upperLimit) return 'optimal';
    if (upperLimit && normalizedAmount > upperLimit) return 'excessive';
    if (rdiRange) {
      if (normalizedAmount >= rdiRange.min && normalizedAmount <= rdiRange.max) return 'optimal';
      if (normalizedAmount < rdiRange.min) return 'below';
      return 'high';
    }
    return 'optimal';
  }

  private getBioavailabilityRating(absorptionRate: number): string {
    if (absorptionRate >= 80) return 'excellent';
    if (absorptionRate >= 60) return 'good';
    if (absorptionRate >= 40) return 'fair';
    return 'poor';
  }

  private getAlternativeForms(profile: IngredientProfile): FormAlternative[] {
    const alternatives: FormAlternative[] = [];
    if (profile.bioavailability.form !== 'chelated') {
      alternatives.push({
        name: 'Chelated form',
        bioavailabilityIncrease: '20-30%',
        cost: 'Higher',
        notes: 'Better absorption, gentler on stomach'
      });
    }
    if (profile.bioavailability.form !== 'liposomal') {
      alternatives.push({
        name: 'Liposomal form',
        bioavailabilityIncrease: '30-50%',
        cost: 'Premium',
        notes: 'Superior absorption, cellular delivery'
      });
    }
    return alternatives;
  }

  private getEvidenceStrength(evidenceLevel: string): string {
    switch (evidenceLevel) {
      case 'A': return 'strong';
      case 'B': return 'moderate';
      case 'C': 
      case 'D': return 'preliminary';
      default: return 'preliminary';
    }
  }

  private calculateEfficacyForCondition(evidenceLevel: string): number {
    switch (evidenceLevel) {
      case 'A': return 85;
      case 'B': return 70;
      case 'C': return 55;
      case 'D': return 40;
      default: return 25;
    }
  }

  private getSourceQuality(profile: IngredientProfile): string {
    const certCount = profile.quality.certifications?.length || 0;
    if (certCount >= 3) return 'pharmaceutical';
    if (certCount >= 2) return 'premium';
    if (certCount >= 1) return 'standard';
    return 'unknown';
  }

  private getShelfStability(profile: IngredientProfile): string {
    const storageReqs = profile.quality.storageRequirements?.length || 0;
    if (storageReqs <= 1) return 'excellent';
    if (storageReqs <= 2) return 'good';
    if (storageReqs <= 3) return 'fair';
    return 'poor';
  }

  private generateIngredientInteractions(
    profile: IngredientProfile,
    demographics?: DemographicFactors
  ): IngredientInteraction[] {
    const interactions: IngredientInteraction[] = [];
    
    if (demographics?.medications?.length && profile.safety.drugInteractions.length > 0) {
      const relevantInteractions = profile.safety.drugInteractions.filter(
        drug => demographics.medications.some(
          med => drug.toLowerCase().includes(med.toLowerCase()) ||
                 med.toLowerCase().includes(drug.toLowerCase())
        )
      );

      relevantInteractions.forEach(drug => {
        interactions.push({
          substance: drug,
          severity: 'moderate',
          effect: 'May alter drug effectiveness or increase side effects',
          mechanism: 'Metabolic pathway interference',
          management: 'Monitor closely and consider dose adjustments'
        });
      });
    }

    return interactions;
  }

  private generateContraindications(
    profile: IngredientProfile,
    demographics?: DemographicFactors
  ): Contraindication[] {
    const contraindications: Contraindication[] = [];
    
    if (demographics?.healthConditions?.length) {
      const relevantContraindications = profile.safety.contraindications.filter(
        contra => demographics.healthConditions.some(
          condition => contra.toLowerCase().includes(condition.toLowerCase()) ||
                      condition.toLowerCase().includes(contra.toLowerCase())
        )
      );

      relevantContraindications.forEach(condition => {
        contraindications.push({
          condition,
          severity: 'absolute',
          reason: 'May worsen existing condition or interfere with treatment',
          source: 'Clinical guidelines'
        });
      });
    }

    return contraindications;
  }

  private generateSideEffects(profile: IngredientProfile): SideEffect[] {
    return profile.safety.sideEffects.map(effect => ({
      effect,
      frequency: 'common' as const,
      severity: 'mild' as const,
      notes: 'Monitor for symptoms and discontinue if severe'
    }));
  }

  private generateSpecialPopulationWarnings(
    profile: IngredientProfile,
    demographics?: DemographicFactors
  ): SpecialPopulationWarning[] {
    const warnings: SpecialPopulationWarning[] = [];
    
    if (demographics?.pregnancyStatus === 'pregnant') {
      if (profile.safety.pregnancyCategory === 'D' || profile.safety.pregnancyCategory === 'X') {
        warnings.push({
          population: 'Pregnant women',
          concern: 'May cause fetal harm',
          recommendation: 'Avoid use during pregnancy'
        });
      }
    }
    
    if (demographics?.pregnancyStatus === 'breastfeeding') {
      if (profile.safety.breastfeedingCategory === 'Avoid') {
        warnings.push({
          population: 'Breastfeeding women',
          concern: 'May pass to breast milk',
          recommendation: 'Avoid use while breastfeeding'
        });
      }
    }
    
    if (demographics?.ageRange?.includes('65+')) {
      warnings.push({
        population: 'Elderly (65+)',
        concern: 'Age-related metabolic changes',
        recommendation: 'Monitor closely and consider lower starting doses'
      });
    }

    return warnings;
  }

  private generateRecommendations(
    profile: IngredientProfile,
    amount: number,
    demographics?: DemographicFactors
  ): Recommendation[] {
    const recommendations: Recommendation[] = [];
    const targetRange = this.getTargetDosageRange(profile, demographics);

    // Dosage recommendations
    if (targetRange && amount < targetRange.min) {
      recommendations.push({
        type: 'dosage',
        priority: 'high',
        title: 'Optimize Dosage',
        description: `Consider increasing dosage to ${targetRange.min}-${targetRange.max} ${targetRange.unit}`,
        impact: 'May improve therapeutic benefits'
      });
    }

    // Timing recommendations
    if (profile.bioavailability.bestTakenWith?.includes('food')) {
      recommendations.push({
        type: 'timing',
        priority: 'medium',
        title: 'Take with meals',
        description: 'Take with food for better absorption',
        impact: 'Improves bioavailability and reduces stomach upset'
      });
    }

    // Storage recommendations
    if (profile.quality.storageRequirements?.length) {
      recommendations.push({
        type: 'form',
        priority: 'low',
        title: 'Proper Storage',
        description: `Store in ${profile.quality.storageRequirements.join(', ')} conditions`,
        impact: 'Maintains potency and extends shelf life'
      });
    }

    return recommendations;
  }

  private generateWarnings(
    profile: IngredientProfile,
    amount: number,
    demographics?: DemographicFactors
  ): Warning[] {
    const warnings: Warning[] = [];
    const upperLimit = this.getUpperLimit(profile, demographics);

    // Dosage warnings
    if (upperLimit && amount > upperLimit.amount) {
      warnings.push({
        severity: 'critical',
        type: 'safety',
        message: `Dosage exceeds safe upper limit of ${upperLimit.amount} ${upperLimit.unit}`,
        recommendation: 'Reduce dosage to safe levels'
      });
    }

    // Pregnancy/breastfeeding warnings
    if (demographics?.pregnancyStatus === 'pregnant') {
      if (profile.safety.pregnancyCategory === 'D' || profile.safety.pregnancyCategory === 'X') {
        warnings.push({
          severity: 'critical',
          type: 'contraindication',
          message: 'Not recommended during pregnancy',
          recommendation: 'Avoid use or consult healthcare provider'
        });
      }
    }

    // Health condition warnings
    if (demographics?.healthConditions?.length) {
      const relevantContraindications = profile.safety.contraindications.filter(
        contra => demographics.healthConditions.some(
          condition => contra.toLowerCase().includes(condition.toLowerCase())
        )
      );

      relevantContraindications.forEach(contra => {
        warnings.push({
          severity: 'high',
          type: 'contraindication',
          message: `Contraindicated with ${contra}`,
          recommendation: 'Consult healthcare provider before use'
        });
      });
    }

    return warnings;
  }

  // Existing calculation methods (simplified versions)
  private normalizeAmount(amount: number, unit: string, profile: IngredientProfile): number {
    const normalizedUnit = unit.toLowerCase().trim();
    const targetUnit = profile.recommendedDailyIntake?.adult.unit.toLowerCase() || 'mg';

    const unitConversions: { [key: string]: { [key: string]: number } } = {
      mg: { g: 0.001, mcg: 1000, 'µg': 1000, iu: 1 },
      g: { mg: 1000, mcg: 1000000, 'µg': 1000000 },
      mcg: { mg: 0.001, g: 0.000001, 'µg': 1 },
      'µg': { mg: 0.001, g: 0.000001, mcg: 1 },
      iu: { iu: 1 },
    };

    if (normalizedUnit === targetUnit) return amount;

    const conversionRate = unitConversions[normalizedUnit]?.[targetUnit];
    if (conversionRate !== undefined) {
      return amount * conversionRate;
    }
    
    // Unknown unit conversion, using original amount
    return amount;
  }

  private getIngredientProfile(ingredientName: string): IngredientProfile | null {
    const normalizedName = ingredientName.toLowerCase().trim();

    // Direct match
    if (this.ingredientDatabase.has(normalizedName)) {
      return this.ingredientDatabase.get(normalizedName)!;
    }

    // Search by common names
    for (const [key, profile] of this.ingredientDatabase) {
      if (
        profile.commonNames.some(
          name =>
            name.toLowerCase().includes(normalizedName) ||
            normalizedName.includes(name.toLowerCase())
        ) ||
        key.includes(normalizedName)
      ) {
        return profile;
      }
    }
    return null;
  }

  private getTargetDosageRange(
    profile: IngredientProfile,
    demographics?: DemographicFactors
  ) {
    if (!profile.recommendedDailyIntake) return null;

    if (demographics?.pregnancyStatus === 'pregnant' && profile.recommendedDailyIntake.pregnant) {
      return profile.recommendedDailyIntake.pregnant;
    }
    if (demographics?.pregnancyStatus === 'breastfeeding' && profile.recommendedDailyIntake.breastfeeding) {
      return profile.recommendedDailyIntake.breastfeeding;
    }
    if ((demographics?.ageRange?.includes('65+') || demographics?.ageRange?.includes('elderly')) && profile.recommendedDailyIntake.elderly) {
      return profile.recommendedDailyIntake.elderly;
    }
    return profile.recommendedDailyIntake.adult;
  }

  private getUpperLimit(
    profile: IngredientProfile,
    demographics?: DemographicFactors
  ) {
    if (!profile.upperLimit) return null;

    if (demographics?.pregnancyStatus === 'pregnant' && profile.upperLimit.pregnant) {
      return profile.upperLimit.pregnant;
    }
    if (demographics?.pregnancyStatus === 'breastfeeding' && profile.upperLimit.breastfeeding) {
      return profile.upperLimit.breastfeeding;
    }
    if ((demographics?.ageRange?.includes('65+') || demographics?.ageRange?.includes('elderly')) && profile.upperLimit.elderly) {
      return profile.upperLimit.elderly;
    }
    return profile.upperLimit.adult;
  }

  private calculateDosageScore(
    amount: number,
    profile: IngredientProfile,
    demographics?: DemographicFactors
  ): number {
    if (!profile.recommendedDailyIntake && !profile.upperLimit) return 70;

    const targetRange = this.getTargetDosageRange(profile, demographics);
    const upperLimit = this.getUpperLimit(profile, demographics);

    if (targetRange) {
      const { min, max } = targetRange;
      if (amount >= min && amount <= max) {
        return 95;
      } else if (amount < min) {
        const ratio = amount / min;
        return Math.max(30, 70 * ratio);
      }
    }
    if (upperLimit && amount > upperLimit.amount) {
      return 20;
    } else if (upperLimit && amount > (targetRange?.max || Infinity)) {
        const excessRatio = (amount - (targetRange?.max || 0)) / (targetRange?.max || 1);
        return Math.max(40, 80 - excessRatio * 30);
    }
    return 70;
  }

  private calculateBioavailabilityScore(profile: IngredientProfile): number {
    const baseScore = profile.bioavailability.absorptionRate;
    let bonus = 0;
    if (profile.bioavailability.form.includes('chelated') || profile.bioavailability.form.includes('liposomal') || profile.bioavailability.form.includes('methylated')) bonus += 10;
    if (profile.bioavailability.form.includes('active')) bonus += 5;
    return Math.min(100, baseScore + bonus);
  }

  private calculateSafetyScore(
    profile: IngredientProfile,
    demographics?: DemographicFactors
  ): number {
    let score = 85;

    if (demographics?.pregnancyStatus === 'pregnant') {
      switch (profile.safety.pregnancyCategory) {
        case 'C': score -= 15; break;
        case 'D': score -= 30; break;
        case 'X': score -= 50; break;
      }
    }
    if (demographics?.pregnancyStatus === 'breastfeeding') {
      switch (profile.safety.breastfeedingCategory) {
        case 'Caution': score -= 10; break;
        case 'Avoid': score -= 40; break;
      }
    }

    if (demographics?.healthConditions) {
      const relevantContraindications = profile.safety.contraindications.filter(
        contra => demographics.healthConditions.some(condition => contra.toLowerCase().includes(condition.toLowerCase()))
      );
      score -= relevantContraindications.length * 15;
    }

    if (demographics?.medications) {
      const relevantInteractions = profile.safety.drugInteractions.filter(
        drug => demographics.medications.some(med => drug.toLowerCase().includes(med.toLowerCase()))
      );
      score -= relevantInteractions.length * 10;
    }
    return Math.max(0, Math.min(100, score));
  }

  private calculateQualityScore(profile: IngredientProfile): number {
    let score = 75;
    if (profile.quality.purityConcerns?.length) score -= profile.quality.purityConcerns.length * 5;
    if (profile.quality.contaminants?.length) score -= profile.quality.contaminants.length * 8;
    if (profile.quality.certifications?.length) score += profile.quality.certifications.length * 3;
    return Math.max(0, Math.min(100, score));
  }

  private calculateEvidenceScore(profile: IngredientProfile): number {
    let score = 50;
    switch (profile.evidence.level) {
      case 'A': score += 30; break;
      case 'B': score += 20; break;
      case 'C': score += 10; break;
    }
    const studyBonus = Math.min(20, Math.log10(profile.evidence.studyCount + 1) * 10);
    score += studyBonus;
    return Math.max(0, Math.min(100, score));
  }

  private calculateOverallScore(scores: {
    dosageScore: number; bioavailabilityScore: number; safetyScore: number;
    qualityScore: number; evidenceScore: number;
  }): number {
    const weights = {
      dosageScore: 0.25, bioavailabilityScore: 0.15, safetyScore: 0.35,
      qualityScore: 0.15, evidenceScore: 0.1,
    };
    return Math.round(
      scores.dosageScore * weights.dosageScore +
      scores.bioavailabilityScore * weights.bioavailabilityScore +
      scores.safetyScore * weights.safetyScore +
      scores.qualityScore * weights.qualityScore +
      scores.evidenceScore * weights.evidenceScore
    );
  }

  /**
   * Initialize the ingredient database with common supplements
   */
  private initializeDatabase(): void {
    this.ingredientDatabase.set('vitamin d3', {
      name: 'Vitamin D3', category: 'vitamin', commonNames: ['cholecalciferol', 'vitamin d', 'd3'],
      recommendedDailyIntake: { adult: { min: 600, max: 2000, unit: 'IU' }, elderly: { min: 800, max: 2000, unit: 'IU' }, pregnant: { min: 600, max: 1000, unit: 'IU' }, breastfeeding: { min: 600, max: 1000, unit: 'IU' }, },
      upperLimit: { adult: { amount: 4000, unit: 'IU' }, elderly: { amount: 4000, unit: 'IU' }, pregnant: { amount: 4000, unit: 'IU' }, breastfeeding: { amount: 4000, unit: 'IU' }, },
      bioavailability: { form: 'D3 (Cholecalciferol)', absorptionRate: 85, bestTakenWith: ['fat', 'food'], avoidWith: [], },
      safety: { pregnancyCategory: 'A', breastfeedingCategory: 'Safe', contraindications: ['hypercalcemia', 'kidney stones', 'sarcoidosis'], sideEffects: ['nausea', 'vomiting', 'weakness'], drugInteractions: ['thiazide diuretics', 'digoxin'], },
      evidence: { level: 'A', studyCount: 500, benefits: ['bone health', 'immune function', 'muscle strength'], conditions: ['osteoporosis', 'rickets', 'immune deficiency'], },
      quality: { purityConcerns: [], contaminants: [], certifications: ['USP', 'NSF'], storageRequirements: ['cool', 'dry', 'dark'], },
    });

    this.ingredientDatabase.set('vitamin c', {
      name: 'Vitamin C', category: 'vitamin', commonNames: ['ascorbic acid', 'l-ascorbic acid', 'ascorbate'],
      recommendedDailyIntake: { adult: { min: 65, max: 500, unit: 'mg' }, elderly: { min: 75, max: 500, unit: 'mg' }, pregnant: { min: 85, max: 500, unit: 'mg' }, breastfeeding: { min: 120, max: 500, unit: 'mg' }, },
      upperLimit: { adult: { amount: 2000, unit: 'mg' }, elderly: { amount: 2000, unit: 'mg' }, pregnant: { amount: 2000, unit: 'mg' }, breastfeeding: { amount: 2000, unit: 'mg' }, },
      bioavailability: { form: 'Ascorbic Acid', absorptionRate: 80, bestTakenWith: ['food'], avoidWith: ['iron supplements'], },
      safety: { pregnancyCategory: 'A', breastfeedingCategory: 'Safe', contraindications: ['kidney stones', 'hemochromatosis'], sideEffects: ['diarrhea', 'nausea', 'stomach cramps'], drugInteractions: ['warfarin', 'chemotherapy drugs'], },
      evidence: { level: 'A', studyCount: 800, benefits: ['immune support', 'antioxidant', 'collagen synthesis'], conditions: ['scurvy', 'immune deficiency', 'wound healing'], },
      quality: { purityConcerns: [], contaminants: [], certifications: ['USP', 'GMP'], storageRequirements: ['cool', 'dry', 'light-protected'], },
    });

    this.ingredientDatabase.set('calcium', {
      name: 'Calcium', category: 'mineral', commonNames: ['calcium carbonate', 'calcium citrate', 'carbonate', 'citrate'],
      recommendedDailyIntake: { adult: { min: 1000, max: 1200, unit: 'mg' }, elderly: { min: 1200, max: 1200, unit: 'mg' }, pregnant: { min: 1000, max: 1300, unit: 'mg' }, breastfeeding: { min: 1000, max: 1300, unit: 'mg' }, },
      upperLimit: { adult: { amount: 2500, unit: 'mg' }, elderly: { amount: 2000, unit: 'mg' }, pregnant: { amount: 2500, unit: 'mg' }, breastfeeding: { amount: 2500, unit: 'mg' }, },
      bioavailability: { form: 'Carbonate', absorptionRate: 40, bestTakenWith: ['food', 'acid'], avoidWith: ['iron', 'zinc', 'fiber'], },
      safety: { pregnancyCategory: 'A', breastfeedingCategory: 'Safe', contraindications: ['kidney stones', 'hypercalcemia'], sideEffects: ['constipation', 'gas', 'bloating'], drugInteractions: ['tetracycline', 'iron', 'thyroid medications'], },
      evidence: { level: 'A', studyCount: 600, benefits: ['bone health', 'muscle function', 'nerve transmission'], conditions: ['osteoporosis', 'osteopenia', 'hypocalcemia'], },
      quality: { purityConcerns: ['lead contamination'], contaminants: ['heavy metals'], certifications: ['USP', 'NSF'], storageRequirements: ['dry', 'room temperature'], },
    });

    this.ingredientDatabase.set('iron', {
      name: 'Iron', category: 'mineral', commonNames: ['ferrous sulfate', 'ferrous gluconate', 'iron sulfate'],
      recommendedDailyIntake: { adult: { min: 8, max: 18, unit: 'mg' }, elderly: { min: 8, max: 8, unit: 'mg' }, pregnant: { min: 27, max: 27, unit: 'mg' }, breastfeeding: { min: 9, max: 9, unit: 'mg' }, },
      upperLimit: { adult: { amount: 45, unit: 'mg' }, elderly: { amount: 45, unit: 'mg' }, pregnant: { amount: 45, unit: 'mg' }, breastfeeding: { amount: 45, unit: 'mg' }, },
      bioavailability: { form: 'Ferrous Sulfate', absorptionRate: 25, bestTakenWith: ['vitamin c', 'empty stomach'], avoidWith: ['calcium', 'coffee', 'tea', 'dairy'], },
      safety: { pregnancyCategory: 'A', breastfeedingCategory: 'Safe', contraindications: ['hemochromatosis', 'hemosiderosis'], sideEffects: ['constipation', 'nausea', 'stomach upset'], drugInteractions: ['tetracycline', 'quinolones', 'levothyroxine'], },
      evidence: { level: 'A', studyCount: 400, benefits: ['oxygen transport', 'energy production', 'immune function'], conditions: ['iron deficiency anemia', 'fatigue'], },
      quality: { purityConcerns: [], contaminants: [], certifications: ['USP', 'GMP'], storageRequirements: ['dry', 'cool', 'child-resistant'], },
    });

    this.ingredientDatabase.set('magnesium', {
      name: 'Magnesium', category: 'mineral', commonNames: ['magnesium citrate', 'magnesium glycinate', 'magnesium oxide'],
      recommendedDailyIntake: { adult: { min: 310, max: 420, unit: 'mg' }, elderly: { min: 350, max: 420, unit: 'mg' }, pregnant: { min: 350, max: 400, unit: 'mg' }, breastfeeding: { min: 310, max: 360, unit: 'mg' }, },
      upperLimit: { adult: { amount: 350, unit: 'mg' }, elderly: { amount: 350, unit: 'mg' }, pregnant: { amount: 350, unit: 'mg' }, breastfeeding: { amount: 350, unit: 'mg' }, },
      bioavailability: { form: 'Glycinate', absorptionRate: 80, bestTakenWith: ['food'], avoidWith: ['zinc', 'fiber'], },
      safety: { pregnancyCategory: 'B', breastfeedingCategory: 'Safe', contraindications: ['kidney disease', 'heart block'], sideEffects: ['diarrhea', 'nausea'], drugInteractions: ['tetracycline', 'digoxin', 'blood pressure medications'], },
      evidence: { level: 'A', studyCount: 300, benefits: ['muscle relaxation', 'nerve function', 'energy production'], conditions: ['muscle cramps', 'anxiety', 'constipation'], },
      quality: { purityConcerns: [], contaminants: [], certifications: ['USP'], storageRequirements: ['cool', 'dry'], },
    });
  }
}

// Export singleton instance for use throughout the app
export const enhancedIngredientAnalyzer = new EnhancedIngredientAnalyzer();