// src/services/analysis/supplementAnalyzer.ts
import { supabase } from '../supabase/client';
import { multiSourceDataService } from '../dataIntegration/multiSourceDataService';
import { productService, scanService, interactionService } from '../database';
import { evidenceService } from '../evidence/evidenceService';
import { fdaService } from '../safety/fdaService';
import type { Product, UserStack, UserProfile, EnhancedIngredientAnalysis, InteractionSeverity, SafetyCheck, StackInteractionResult, Interaction } from '../../types';
import { APP_CONFIG } from '../../constants/index';
import { enhancedIngredientAnalyzer } from './enhancedIngredientAnalyzer';

export interface EnhancedSupplementAnalysis {
  overallScore: number;
  riskLevel: InteractionSeverity;
  confidenceLevel: number;
  evidenceGrade: 'A' | 'B' | 'C' | 'D' | 'F';
  categoryScores: {
    safety: number;
    evidence: number;
    dosage: number;
    bioavailability: number;
    quality: number;
    purity: number;
  };
  ingredientAnalyses: EnhancedIngredientAnalysis[];
  clinicalAssessment: {
    primaryUses: string[];
    efficacySummary: string;
    evidenceSources: string[];
  };
  personalizedRecommendations: {
    dosageAdjustments: string[];
    formRecommendations: string[];
    timingSuggestions: string[];
    warnings: string[];
  };
  safetyProfile: SafetyCheck;
  qualityAssessment: {
    overallQuality: 'low' | 'medium' | 'high';
    certifications: string[];
    contaminantRisk: 'low' | 'medium' | 'high';
  };
  evidenceListing: {
    clinicalSupport: string[];
    researchGaps: string[];
    alternativeOptions: string[];
  };
  stackCompatibility?: StackInteractionResult;
  discrepancies?: string[];
  sources?: string[];
}

export class SupplementAnalyzer {
  private ingredientAnalyzer: EnhancedIngredientAnalyzer;

  constructor() {
    this.ingredientAnalyzer = enhancedIngredientAnalyzer;
  }

  async analyzeSupplementComprehensive(
    product: Product,
    healthProfile?: UserProfile,
    currentStack?: UserStack[]
  ): Promise<EnhancedSupplementAnalysis> {
    const productData = await multiSourceDataService.fetchProductByBarcode(product.barcode || '');
    const ingredientAnalyses = await this.analyzeIngredients({ ...product, ...productData }, healthProfile);
    const feedback = await this.incorporateFeedback(productData || product);

    const categoryScores = this.calculateCategoryScores(ingredientAnalyses);
    const clinicalAssessment = await this.performClinicalAssessment(ingredientAnalyses, healthProfile);
    const personalizedRecommendations = this.generatePersonalizedRecommendations(ingredientAnalyses, healthProfile);
    const safetyProfile = await this.assessSafetyProfile(ingredientAnalyses, healthProfile);
    const qualityAssessment = this.evaluateQuality(ingredientAnalyses, productData);
    const evidenceListing = this.summarizeEvidence(ingredientAnalyses);
    const stackCompatibility = currentStack && currentStack.length > 0
      ? await this.analyzeStackCompatibility(productData || product, currentStack, healthProfile)
      : undefined;

    const overallScore = this.calculateOverallScore(categoryScores, clinicalAssessment, healthProfile);
    const riskLevel = this.determineRiskLevel(safetyProfile, clinicalAssessment);
    const confidenceLevel = this.calculateConfidenceLevel(ingredientAnalyses, productData?.discrepancies || []);
    const evidenceGrade = this.determineEvidenceGrade(ingredientAnalyses);

    if (product.id) {
      await scanService.recordScan(
        healthProfile?.id || null,
        product.id,
        'barcode',
        overallScore
      );
    }

    if (!product.id && productData) {
      const newProduct = await productService.create({
        ...product,
        name: productData.name,
        brand: productData.brand,
        category: productData.category,
        ingredients: productData.ingredients,
        imageUrl: productData.imageUrl,
        verified: false,
        thirdPartyTested: false,
        certifications: [],
      });
      if (newProduct) product.id = newProduct.id;
    }

    return {
      overallScore,
      riskLevel,
      confidenceLevel,
      evidenceGrade,
      categoryScores,
      ingredientAnalyses,
      clinicalAssessment,
      personalizedRecommendations,
      safetyProfile,
      qualityAssessment,
      evidenceListing,
      stackCompatibility,
      discrepancies: productData?.discrepancies || feedback.map(f => f.issue),
      sources: productData?.sources,
    };
  }

  private async incorporateFeedback(product: Product) {
    try {
      const { data: feedback } = await supabase
        .from('feedback_reports')
        .select('*')
        .eq('related_product_id', product.id)
        .eq('status', 'RESOLVED');
      return feedback?.map(f => ({
        issue: f.description,
        resolution: f.resolution,
        ruleId: f.resulted_in_rule_id,
      })) || [];
    } catch (error) {
      console.error('Error incorporating feedback:', error);
      return [];
    }
  }

  private async analyzeIngredients(product: Product & { discrepancies?: string[] }, userProfile?: UserProfile): Promise<EnhancedIngredientAnalysis[]> {
    const analyses: EnhancedIngredientAnalysis[] = [];
    for (const ingredient of product.ingredients) {
      const analysis = await this.ingredientAnalyzer.analyzeIngredient(
        ingredient.name,
        parseFloat(ingredient.amount?.toString() || '0') || 0,
        ingredient.unit || 'mg',
        userProfile
      );
      if (product.discrepancies?.length) {
        analysis.warnings.push(...product.discrepancies
          .filter(d => d.toLowerCase().includes(ingredient.name.toLowerCase()))
          .map(d => ({
            severity: 'MODERATE' as InteractionSeverity,
            type: 'discrepancy' as const,
            message: d,
            recommendation: 'Verify product label accuracy',
          })));
      }
      analyses.push(analysis);
    }
    return analyses;
  }

  private async assessSafetyProfile(
    analyses: EnhancedIngredientAnalysis[],
    userProfile?: UserProfile
  ): Promise<SafetyCheck> {
    const interactions: Interaction[] = [];
    const contraindications: string[] = [];
    const warnings: string[] = [];

    for (const analysis of analyses) {
      analysis.safetyAnalysis.interactions.forEach(i => {
        interactions.push({
          id: `int_${analysis.ingredient}_${i.substance}`,
          type: i.substance.includes('drug') ? 'drug-supplement' : 'supplement-supplement',
          severity: i.severity,
          substance1: { name: analysis.ingredient, category: 'supplement', commonNames: [analysis.ingredient] },
          substance2: { name: i.substance, category: i.substance.includes('drug') ? 'medication' : 'supplement', commonNames: [i.substance] },
          mechanism: i.mechanism || 'unknown',
          description: i.effect,
          clinicalSignificance: i.effect,
          evidence: {
            level: 'B',
            sources: analysis.dataSources,
            studyCount: 0,
            lastUpdated: new Date().toISOString(),
          },
          recommendations: {
            action: i.management.includes('consult') ? 'consult_provider' : 'monitor',
            monitoring: [i.management],
          },
        });
      });
      analysis.safetyAnalysis.contraindications.forEach(c => {
        contraindications.push(`${analysis.ingredient}: ${c.condition} (${c.reason})`);
      });
      if (analysis.safetyAnalysis.percentOfUpperLimit && analysis.safetyAnalysis.percentOfUpperLimit > 100) {
        warnings.push(
          `${analysis.ingredient}: Exceeds upper limit (${analysis.safetyAnalysis.percentOfUpperLimit.toFixed(1)}% of UL)`
        );
      }
      const adverseEvents = await fdaService.getAdverseEvents(analysis.ingredient);
      adverseEvents.forEach(event => {
        warnings.push(`${analysis.ingredient}: ${event.event} (Severity: ${event.severity}, Reported: ${event.date})`);
      });
      const drugLabel = await fdaService.getDrugLabel(analysis.ingredient);
      if (drugLabel) {
        warnings.push(...drugLabel.warnings.map(w => `${analysis.ingredient}: ${w}`));
        contraindications.push(...drugLabel.contraindications.map(c => `${analysis.ingredient}: ${c}`));
        interactions.push(...drugLabel.interactions.map(i => ({
          id: `openfda_${analysis.ingredient}_${i}`,
          type: 'drug-supplement',
          severity: 'MODERATE',
          substance1: { name: analysis.ingredient, category: 'supplement', commonNames: [analysis.ingredient] },
          substance2: { name: i, category: 'medication', commonNames: [i] },
          mechanism: 'unknown',
          description: i,
          clinicalSignificance: 'Potential interaction identified in drug labeling',
          evidence: {
            level: 'B',
            sources: ['OpenFDA Drug Labeling'],
            studyCount: 0,
            lastUpdated: new Date().toISOString(),
          },
          recommendations: {
            action: 'consult_provider',
            monitoring: ['Monitor for adverse effects'],
          },
        })));
      }
      const drugInteractions = await fdaService.getDrugInteractions(analysis.ingredient, []);
      interactions.push(...drugInteractions);
    }

    const status = this.determineSafetyStatus(interactions, contraindications, warnings);

    return {
      status,
      interactions,
      contraindications,
      warnings,
    };
  }

  private calculateCategoryScores(analyses: EnhancedIngredientAnalysis[]): EnhancedSupplementAnalysis['categoryScores'] {
    const totalWeight = analyses.length || 1;
    return {
      safety: analyses.reduce((sum, a) => sum + a.scores.safety, 0) / totalWeight,
      evidence: analyses.reduce((sum, a) => sum + a.scores.evidence, 0) / totalWeight,
      dosage: analyses.reduce((sum, a) => sum + a.scores.dosage, 0) / totalWeight,
      bioavailability: analyses.reduce((sum, a) => sum + a.scores.bioavailability, 0) / totalWeight,
      quality: analyses.reduce((sum, a) => sum + a.scores.quality, 0) / totalWeight,
      purity: analyses.reduce((sum, a) => sum + a.scores.purity, 0) / totalWeight,
    };
  }

  private calculateOverallScore(
    categoryScores: EnhancedSupplementAnalysis['categoryScores'],
    clinicalAssessment: EnhancedSupplementAnalysis['clinicalAssessment'],
    userProfile?: UserProfile
  ): number {
    const weights = {
      safety: userProfile?.demographics?.pregnancyStatus === 'pregnant' ? 0.40 : 0.30,
      evidence: 0.20,
      dosage: 0.15,
      bioavailability: 0.15,
      quality: 0.10,
      purity: 0.10,
    };
    const clinicalScore = clinicalAssessment.primaryUses.length > 0
      ? (clinicalAssessment.evidenceSources.length / 5) * 100
      : 50;
    const score = Object.entries(categoryScores).reduce(
      (sum, [key, value]) => sum + value * (weights as any)[key],
      0
    ) * 0.7 + clinicalScore * 0.3;
    return Math.round(Math.max(0, Math.min(100, score)));
  }

  private async performClinicalAssessment(
    analyses: EnhancedIngredientAnalysis[],
    healthProfile?: UserProfile
  ): Promise<EnhancedSupplementAnalysis['clinicalAssessment']> {
    const primaryUses: string[] = [];
    const evidenceSources: string[] = [];
    let efficacySummary = 'No significant clinical evidence found.';

    for (const analysis of analyses) {
      primaryUses.push(...analysis.clinicalEvidence.primaryUses.map(u => u.condition));
      evidenceSources.push(...analysis.dataSources);
      if (analysis.clinicalEvidence.studyCount > 0) {
        efficacySummary = `Found ${analysis.clinicalEvidence.studyCount} studies, including ${analysis.clinicalEvidence.metaAnalysisCount} meta-analyses.`;
      }
    }

    if (healthProfile?.healthGoals) {
      const goalMatches = analyses.filter(a =>
        a.clinicalEvidence.primaryUses.some(u => healthProfile.healthGoals.includes(u.condition))
      );
      if (goalMatches.length > 0) {
        efficacySummary = `Evidence supports use for ${goalMatches.length} of your health goals: ${goalMatches.map(a => a.ingredient).join(', ')}.`;
      }
    }

    return {
      primaryUses: Array.from(new Set(primaryUses)),
      efficacySummary,
      evidenceSources: Array.from(new Set(evidenceSources)),
    };
  }

  private generatePersonalizedRecommendations(
    analyses: EnhancedIngredientAnalysis[],
    userProfile?: UserProfile
  ): EnhancedSupplementAnalysis['personalizedRecommendations'] {
    const dosageAdjustments: string[] = [];
    const formRecommendations: string[] = [];
    const timingSuggestions: string[] = [];
    const warnings: string[] = [];

    analyses.forEach(analysis => {
      if (analysis.dosageAnalysis.demographicAdjustment) {
        dosageAdjustments.push(`${analysis.ingredient}: ${analysis.dosageAnalysis.demographicAdjustment}`);
      }
      if (analysis.formAnalysis.alternativeForms.length > 0) {
        formRecommendations.push(
          `${analysis.ingredient}: Consider ${analysis.formAnalysis.alternativeForms[0].name} for better absorption (+${analysis.formAnalysis.alternativeForms[0].bioavailabilityIncrease})`
        );
      }
      analysis.formAnalysis.absorptionFactors.forEach(factor => {
        timingSuggestions.push(`${analysis.ingredient}: ${factor.timing} with ${factor.factor} (${factor.effect} absorption by ${factor.magnitude})`);
      });
      analysis.warnings.forEach(w => warnings.push(`${analysis.ingredient}: ${w.message} (${w.recommendation})`));
    });

    return {
      dosageAdjustments,
      formRecommendations,
      timingSuggestions,
      warnings,
    };
  }

  private async analyzeStackCompatibility(
    product: Product,
    stack: UserStack[],
    healthProfile?: UserProfile
  ): Promise<StackInteractionResult> {
    const interactions = await interactionService.analyzeProductWithStack(product, stack);
    return interactions;
  }

  private determineRiskLevel(
    safetyProfile: SafetyCheck,
    clinicalAssessment: EnhancedSupplementAnalysis['clinicalAssessment']
  ): InteractionSeverity {
    if (safetyProfile.interactions.some(i => i.severity === 'CRITICAL')) return 'CRITICAL';
    if (safetyProfile.interactions.some(i => i.severity === 'HIGH')) return 'HIGH';
    if (safetyProfile.interactions.some(i => i.severity === 'MODERATE')) return 'MODERATE';
    return 'LOW';
  }

  private calculateConfidenceLevel(
    analyses: EnhancedIngredientAnalysis[],
    discrepancies: string[]
  ): number {
    const baseConfidence = analyses.reduce((sum, a) => sum + a.confidenceScore, 0) / (analyses.length || 1);
    const discrepancyPenalty = discrepancies.length * 0.1;
    return Math.max(0, Math.min(1, baseConfidence - discrepancyPenalty)) * 100;
  }

  private determineEvidenceGrade(analyses: EnhancedIngredientAnalysis[]): 'A' | 'B' | 'C' | 'D' | 'F' {
    const evidenceLevels = analyses.map(a => a.clinicalEvidence.evidenceLevel);
    if (evidenceLevels.every(level => level === 'A')) return 'A';
    if (evidenceLevels.some(level => level === 'A' || level === 'B')) return 'B';
    if (evidenceLevels.some(level => level === 'C')) return 'C';
    if (evidenceLevels.some(level => level === 'D')) return 'D';
    return 'F';
  }

  private evaluateQuality(analyses: EnhancedIngredientAnalysis[], productData: any): EnhancedSupplementAnalysis['qualityAssessment'] {
    const certifications = productData?.certifications || [];
    const qualityScores = analyses.map(a => a.scores.quality);
    const avgQuality = qualityScores.reduce((sum, score) => sum + score, 0) / (qualityScores.length || 1);
    
    return {
      overallQuality: avgQuality > 80 ? 'high' : avgQuality > 60 ? 'medium' : 'low',
      certifications,
      contaminantRisk: avgQuality < 70 ? 'high' : avgQuality < 90 ? 'medium' : 'low',
    };
  }

  private summarizeEvidence(analyses: EnhancedIngredientAnalysis[]): EnhancedSupplementAnalysis['evidenceListing'] {
    const clinicalSupport: string[] = [];
    const researchGaps: string[] = [];
    const alternativeOptions: string[] = [];

    analyses.forEach(analysis => {
      clinicalSupport.push(...analysis.clinicalEvidence.primaryUses.map(u => `${analysis.ingredient}: ${u.condition}`));
      if (analysis.clinicalEvidence.studyCount < 3) {
        researchGaps.push(`${analysis.ingredient}: Limited studies (${analysis.clinicalEvidence.studyCount})`);
      }
      analysis.formAnalysis.alternativeForms.forEach(form => {
        alternativeOptions.push(`${analysis.ingredient}: ${form.name} (Bioavailability: ${form.bioavailabilityIncrease})`);
      });
    });

    return {
      clinicalSupport,
      researchGaps,
      alternativeOptions,
    };
  }

  private determineSafetyStatus(
    interactions: Interaction[],
    contraindications: string[],
    warnings: string[]
  ): 'SAFE' | 'CAUTION' | 'DANGER' {
    if (interactions.some(i => i.severity === 'CRITICAL') || contraindications.length > 0) {
      return 'DANGER';
    }
    if (interactions.some(i => i.severity === 'HIGH' || i.severity === 'MODERATE') || warnings.length > 0) {
      return 'CAUTION';
    }
    return 'SAFE';
  }
}

export const supplementAnalyzer = new SupplementAnalyzer();