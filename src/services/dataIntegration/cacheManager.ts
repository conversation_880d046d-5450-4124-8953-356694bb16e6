// src/services/dataIntegration/cacheManager.ts
import { mmkvStorage } from '../storage/mmkvStorage';

export class CacheManager {
  private readonly CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours

  async getCachedOrFetch<T>(key: string, fetcher: () => Promise<T>): Promise<T> {
    const cached = mmkvStorage.getString(key);
    if (cached) {
      const { data, timestamp } = JSON.parse(cached);
      if (Date.now() - timestamp < this.CACHE_DURATION) {
        return data;
      }
    }

    const fresh = await fetcher();
    mmkvStorage.set(key, JSON.stringify({ data: fresh, timestamp: Date.now() }));
    return fresh;
  }

  clearCache(key: string): void {
    mmkvStorage.delete(key);
  }

  async clearExpiredCache(): Promise<void> {
    const keys = mmkvStorage.getAllKeys();
    for (const key of keys) {
      const cached = mmkvStorage.getString(key);
      if (cached) {
        const { timestamp } = JSON.parse(cached);
        if (Date.now() - timestamp > this.CACHE_DURATION) {
          mmkvStorage.delete(key);
        }
      }
    }
  }
}

export const cacheManager = new CacheManager();