// src/services/dataIntegration/multiSourceDataService.ts
import axios from 'axios';
import { supabase } from '../supabase/client';
import { Product, Ingredient, NutrientRDI, DatabaseSourceReference } from '../../types';
import { AICache } from '../ai/AICache';
import { STORAGE_KEYS, NUTRIENT_LIMITS, API_ENDPOINTS, APP_CONFIG } from '../../constants/index';
import { enhancedIngredientAnalyzer } from '../analysis/enhancedIngredientAnalyzer';
import { logger } from '../logging/logger';

// Rate limit tracking
const rateLimits = {
  DSLD: { limit: 1000, windowMs: 60 * 60 * 1000, count: 0, resetTime: Date.now() },
  OpenFoodFacts: { limit: 100, windowMs: 60 * 1000, count: 0, resetTime: Date.now() },
};

function checkRateLimit(api: 'DSLD' | 'OpenFoodFacts'): boolean {
  const limit = rateLimits[api];
  const now = Date.now();
  if (now > limit.resetTime + limit.windowMs) {
    limit.count = 0;
    limit.resetTime = now;
  }
  if (limit.count >= limit.limit) {
    logger.warn(`Rate limit exceeded for ${api}`, 'MultiSourceDataService', { resetTime: new Date(limit.resetTime + limit.windowMs).toISOString() });
    return false;
  }
  limit.count++;
  return true;
}

export class MultiSourceDataService {
  private aiCache: AICache;

  constructor() {
    this.aiCache = new AICache();
  }

  async fetchProductByBarcode(barcode: string, userId?: string): Promise<Product | null> {
    const normalizedBarcode = barcode.replace(/\s/g, '');
    const spacedBarcode = barcode.trim();

    // Check cache first (Tier 2)
    const cached = await this.aiCache.get(`${STORAGE_KEYS.PRODUCT_CACHE}:${barcode}`);
    if (cached) {
      return cached as Product;
    }

    // Supabase check
    try {
      const { data, error } = await supabase
        .from('products')
        .select('*')
        .eq('barcode', normalizedBarcode)
        .single();
      if (error) {
        logger.error('Supabase product fetch failed', 'MultiSourceDataService', error);
      } else if (data) {
        const product = this.mapSupabaseToProduct(data);
        await this.aiCache.set(`${STORAGE_KEYS.PRODUCT_CACHE}:${barcode}`, product);
        return product;
      }
    } catch (e) {
      logger.error('Supabase product fetch exception', 'MultiSourceDataService', e);
    }

    // DSLD (Tier 3)
    if (checkRateLimit('DSLD')) {
      try {
        const response = await axios.get(`${API_ENDPOINTS.DSLD}/browse-products/`, {
          params: { method: 'by_keyword', q: `upcSku:${spacedBarcode}` },
          headers: { 'X-Api-Key': APP_CONFIG.DSLD_API_KEY, 'User-Agent': APP_CONFIG.USER_AGENT },
          timeout: APP_CONFIG.API_TIMEOUT_MS,
        });
        if (response.data.hits && response.data.hits.length > 0) {
          const product = this.mapDSLDToProduct(response.data.hits[0]._source);
          await this.aiCache.set(`${STORAGE_KEYS.PRODUCT_CACHE}:${barcode}`, product);
          await this.saveProduct(product);
          return product;
        }
      } catch (e) {
        logger.error('DSLD barcode fetch failed', 'MultiSourceDataService', e);
      }
    }

    // USDA fallback
    try {
      const response = await axios.get(`${API_ENDPOINTS.USDA}/foods/search`, {
        params: { api_key: APP_CONFIG.USDA_API_KEY, query: `gtinUpc:${normalizedBarcode}`, pageSize: 1 },
        headers: { 'User-Agent': APP_CONFIG.USER_AGENT },
        timeout: APP_CONFIG.API_TIMEOUT_MS,
      });
      if (response.data.foods && response.data.foods[0]) {
        const product = this.mapUSDAToProduct(response.data.foods[0]);
        await this.aiCache.set(`${STORAGE_KEYS.PRODUCT_CACHE}:${barcode}`, product);
        await this.saveProduct(product);
        return product;
      }
    } catch (e) {
      console.error('USDA barcode fetch error:', e);
    }

    // Open Food Facts fallback
    if (checkRateLimit('OpenFoodFacts')) {
      try {
        const headers: any = { 'User-Agent': APP_CONFIG.USER_AGENT };
        if (process.env.NODE_ENV === 'development') {
          headers['Authorization'] = 'Basic ' + Buffer.from('off:off').toString('base64');
        }
        const response = await axios.get(`${API_ENDPOINTS.OpenFoodFacts}/product/${normalizedBarcode}.json`, {
          headers,
          timeout: APP_CONFIG.API_TIMEOUT_MS,
        });
        if (response.data.status === 1) {
          const product = this.mapOpenFoodFactsToProduct(response.data.product);
          await this.aiCache.set(`${STORAGE_KEYS.PRODUCT_CACHE}:${barcode}`, product);
          await this.saveProduct(product);
          return product;
        }
      } catch (e) {
        console.error('Open Food Facts barcode fetch error:', e);
      }
    }

    return null;
  }

  async fetchProductsByName(name: string): Promise<Product[]> {
    const products: Product[] = [];

    // Check cache (Tier 2)
    const cached = await this.aiCache.get(`${STORAGE_KEYS.PRODUCT_CACHE}:${name}`);
    if (cached) {
      return Array.isArray(cached) ? cached : [cached];
    }

    // Supabase check
    try {
      const { data, error } = await supabase
        .from('products')
        .select('*')
        .ilike('name', `%${name}%`);
      if (error) {
        console.error('Supabase name search error:', error);
      } else if (data) {
        const supabaseProducts = data.map(this.mapSupabaseToProduct);
        products.push(...supabaseProducts);
        await this.aiCache.set(`${STORAGE_KEYS.PRODUCT_CACHE}:${name}`, products);
      }
    } catch (e) {
      console.error('Supabase name search error:', e);
    }

    // DSLD
    if (checkRateLimit('DSLD')) {
      try {
        const response = await axios.get(`${API_ENDPOINTS.DSLD}/browse-products/`, {
          params: { method: 'by_keyword', q: name },
          headers: { 'X-Api-Key': APP_CONFIG.DSLD_API_KEY, 'User-Agent': APP_CONFIG.USER_AGENT },
          timeout: APP_CONFIG.API_TIMEOUT_MS,
        });
        if (response.data.hits && response.data.hits.length > 0) {
          const dslProducts = response.data.hits.map((hit: any) => this.mapDSLDToProduct(hit._source));
          products.push(...dslProducts);
          await this.aiCache.set(`${STORAGE_KEYS.PRODUCT_CACHE}:${name}`, products);
        }
      } catch (e) {
        console.error('DSLD name search error:', e);
      }
    }

    // USDA fallback
    try {
      const response = await axios.get(`${API_ENDPOINTS.USDA}/foods/search`, {
        params: { api_key: APP_CONFIG.USDA_API_KEY, query: name, pageSize: 10 },
        headers: { 'User-Agent': APP_CONFIG.USER_AGENT },
        timeout: APP_CONFIG.API_TIMEOUT_MS,
      });
      if (response.data.foods) {
        const usdaProducts = response.data.foods.map((food: any) => this.mapUSDAToProduct(food));
        products.push(...usdaProducts);
        await this.aiCache.set(`${STORAGE_KEYS.PRODUCT_CACHE}:${name}`, products);
      }
    } catch (e) {
      console.error('USDA name search error:', e);
    }

    return products;
  }

  async searchWithFilters(options: {
    query?: string;
    productName?: string;
    brand?: string;
    ingredientName?: string;
    productType?: string;
    supplementForm?: string;
    limit?: number;
  }): Promise<Product[]> {
    const cacheKey = `${STORAGE_KEYS.PRODUCT_CACHE}:filter:${JSON.stringify(options)}`;
    const cached = await this.aiCache.get(cacheKey);
    if (cached) {
      return Array.isArray(cached) ? cached : [cached];
    }

    const products: Product[] = [];

    // Supabase check
    try {
      let query = supabase.from('products').select('*');
      if (options.productName) query = query.ilike('name', `%${options.productName}%`);
      if (options.brand) query = query.ilike('brand', `%${options.brand}%`);
      if (options.ingredientName) query = query.contains('ingredients', [{ name: options.ingredientName }]);
      if (options.productType) query = query.eq('category', options.productType);
      const { data, error } = await query.limit(options.limit || 20);
      if (error) {
        console.error('Supabase filter search error:', error);
      } else if (data) {
        const supabaseProducts = data.map(this.mapSupabaseToProduct);
        products.push(...supabaseProducts);
      }
    } catch (e) {
      console.error('Supabase filter search error:', e);
    }

    // DSLD
    if (checkRateLimit('DSLD')) {
      try {
        const params = new URLSearchParams();
        params.append('q', options.query || options.productName || '*');
        params.append('method', 'by_keyword');
        params.append('from', '0');
        params.append('size', (options.limit || 20).toString());
        if (options.brand) params.append('brand', options.brand);
        if (options.ingredientName) params.append('ingredient_name', options.ingredientName);
        if (options.productType) params.append('product_type', options.productType);
        if (options.supplementForm) params.append('supplement_form', options.supplementForm);

        const response = await axios.get(`${API_ENDPOINTS.DSLD}/browse-products/`, {
          params,
          headers: { 'X-Api-Key': APP_CONFIG.DSLD_API_KEY, 'User-Agent': APP_CONFIG.USER_AGENT },
          timeout: APP_CONFIG.API_TIMEOUT_MS,
        });
        if (response.data.hits && response.data.hits.length > 0) {
          const dslProducts = response.data.hits.map((hit: any) => this.mapDSLDToProduct(hit._source));
          products.push(...dslProducts);
        }
      } catch (e) {
        console.error('DSLD filter search error:', e);
      }
    }

    await this.aiCache.set(cacheKey, products);
    return products;
  }

  async fetchDrugInteractions(drugName: string): Promise<string[]> {
    const interactions: string[] = [];

    // Check cache
    const cached = await this.aiCache.get(`${STORAGE_KEYS.INTERACTION_CACHE}:${drugName}`);
    if (cached) {
      return cached;
    }

    try {
      const response = await axios.get(`${API_ENDPOINTS.FDA}/drug/label.json`, {
        params: {
          api_key: APP_CONFIG.FDA_API_KEY,
          search: `openfda.generic_name:"${drugName}" AND drug_interactions:*`,
          limit: 10,
        },
        headers: { 'User-Agent': APP_CONFIG.USER_AGENT },
        timeout: APP_CONFIG.API_TIMEOUT_MS,
      });
      if (response.data.results) {
        response.data.results.forEach((result: any) => {
          if (result.drug_interactions) {
            interactions.push(...result.drug_interactions);
          }
        });
        await this.aiCache.set(`${STORAGE_KEYS.INTERACTION_CACHE}:${drugName}`, interactions);
      }
    } catch (e) {
      console.error('FDA drug interaction error:', e);
    }

    return interactions;
  }

  async fetchAdverseEvents(drugName: string): Promise<string[]> {
    const events: string[] = [];

    // Check cache
    const cached = await this.aiCache.get(`${STORAGE_KEYS.ADVERSE_EVENT_CACHE}:${drugName}`);
    if (cached) {
      return cached;
    }

    try {
      const response = await axios.get(`${API_ENDPOINTS.FDA}/drug/event.json`, {
        params: {
          api_key: APP_CONFIG.FDA_API_KEY,
          search: `patient.drug.openfda.generic_name:"${drugName}"`,
          limit: 10,
        },
        headers: { 'User-Agent': APP_CONFIG.USER_AGENT },
        timeout: APP_CONFIG.API_TIMEOUT_MS,
      });
      if (response.data.results) {
        response.data.results.forEach((result: any) => {
          if (result.patient.reaction) {
            const reactions = Array.isArray(result.patient.reaction)
              ? result.patient.reaction
              : [result.patient.reaction];
            events.push(...reactions.map((r: any) => r.reactionmeddrapt));
          }
        });
        await this.aiCache.set(`${STORAGE_KEYS.ADVERSE_EVENT_CACHE}:${drugName}`, events);
      }
    } catch (e) {
      console.error('FDA adverse event error:', e);
    }

    return events;
  }

  async searchPubMedEvidence(query: string): Promise<DatabaseSourceReference[]> {
    const evidence: DatabaseSourceReference[] = [];

    // Check cache
    const cached = await this.aiCache.get(`${STORAGE_KEYS.EVIDENCE_CACHE}:${query}`);
    if (cached) {
      return cached;
    }

    try {
      const response = await axios.get(`${API_ENDPOINTS.PubMed}/esearch.fcgi`, {
        params: {
          db: 'pubmed',
          term: query,
          retmode: 'json',
          retmax: 10,
          api_key: APP_CONFIG.PUBMED_API_KEY,
        },
        headers: { 'User-Agent': APP_CONFIG.USER_AGENT },
        timeout: APP_CONFIG.API_TIMEOUT_MS,
      });
      if (response.data.esearchresult.idlist) {
        for (const pmid of response.data.esearchresult.idlist) {
          const details = await axios.get(`${API_ENDPOINTS.PubMed}/efetch.fcgi`, {
            params: {
              db: 'pubmed',
              id: pmid,
              rettype: 'abstract',
              retmode: 'text',
              api_key: APP_CONFIG.PUBMED_API_KEY,
            },
            headers: { 'User-Agent': APP_CONFIG.USER_AGENT },
            timeout: APP_CONFIG.API_TIMEOUT_MS,
          });
          evidence.push({
            source: 'PubMed',
            id: pmid,
            abstract: details.data.substring(0, 500),
            url: `https://pubmed.ncbi.nlm.nih.gov/${pmid}/`,
          });
        }
        await this.aiCache.set(`${STORAGE_KEYS.EVIDENCE_CACHE}:${query}`, evidence);
      }
    } catch (e) {
      console.error('PubMed evidence fetch error:', e);
    }

    return evidence;
  }

  private mapDSLDToProduct(data: any): Product {
    const ingredients = this.parseIngredientsList(data.allIngredients || data.ingredientRows || []);
    return {
      id: `dsld_${data.id || data.dsld_id}`,
      name: data.fullName || data.product_name || 'Unknown Product',
      brand: data.brandName || data.brand_name || 'Unknown Brand',
      barcode: data.upcSku || data.upc,
      ingredients,
      nutrients: ingredients.map((ing: Ingredient) => ({
        name: ing.name,
        amount: ing.quantity || ing.amount || 0,
        unit: ing.unit || 'mg',
        percentDV: ing.percentDV || ing.dailyValuePercentage || 0,
      })),
      source: 'DSLD',
      sourceId: data.id.toString() || data.dsld_id,
      category: this.mapDSLDCategory(data.productType?.langualCodeDescription || data.product_type),
      servingSize: this.extractServingSize(data.servingSizes || data.serving_size),
      servingsPerContainer: data.servingsPerContainer || data.servings_per_container || 1,
      verified: true,
      thirdPartyTested: false,
      certifications: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
  }

  private mapUSDAToProduct(data: any): Product {
    const ingredients = this.parseIngredientsList(data.ingredients ? [data.ingredients] : []);
    return {
      id: data.fdcId.toString(),
      name: data.description,
      brand: data.brandName || 'Unknown',
      barcode: data.gtinUpc,
      ingredients,
      nutrients: (data.foodNutrients || []).map((nutrient: any) => ({
        name: nutrient.nutrient.name,
        amount: nutrient.amount || 0,
        unit: nutrient.nutrient.unitName || 'mg',
        percentDV: nutrient.percentDailyValue || 0,
      })),
      source: 'USDA',
      sourceId: data.fdcId.toString(),
      category: 'supplement',
      servingSize: data.servingSize || 'Unknown',
      servingsPerContainer: data.servingsPerContainer || 1,
      verified: false,
      thirdPartyTested: false,
      certifications: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
  }

  private mapOpenFoodFactsToProduct(data: any): Product {
    const ingredients = this.parseIngredientsList(data.ingredients || []);
    return {
      id: data.code,
      name: data.product_name,
      brand: data.brands || 'Unknown',
      barcode: data.code,
      ingredients,
      nutrients: (data.nutriments || []).map((nutrient: any) => ({
        name: nutrient.name,
        amount: nutrient.value || 0,
        unit: nutrient.unit || 'mg',
        percentDV: nutrient.percentDV || 0,
      })),
      source: 'OpenFoodFacts',
      sourceId: data.code,
      category: 'supplement',
      servingSize: data.serving_size || 'Unknown',
      servingsPerContainer: data.quantity ? parseInt(data.quantity) : 1,
      verified: false,
      thirdPartyTested: false,
      certifications: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
  }

  private parseIngredientsList(ingredientsList: any[]): Ingredient[] {
    if (!Array.isArray(ingredientsList)) return [];
    
    return ingredientsList.map((item: any) => {
      if (typeof item === 'string') {
        return {
          name: item,
          amount: null,
          unit: null,
          form: 'other' as const,
          bioavailability: 'medium' as const,
          evidenceLevel: 'observational' as const,
          category: 'active' as const
        };
      }
      
      return {
        name: item.name || item.ingredient_name || item.description || 'Unknown',
        amount: item.amount || item.quantity || null,
        unit: item.unit || item.unit_name || null,
        form: item.form || 'other' as const,
        bioavailability: item.bioavailability || 'medium' as const,
        evidenceLevel: item.evidenceLevel || 'observational' as const,
        category: item.category || 'active' as const
      };
    });
  }

  private mapSupabaseToProduct(data: any): Product {
    return {
      id: data.id,
      name: data.name,
      brand: data.brand,
      barcode: data.barcode,
      ingredients: data.ingredients || [],
      nutrients: data.nutrients || [],
      source: data.source || 'Supabase',
      sourceId: data.source_id || data.id,
      category: data.category || 'supplement',
      servingSize: data.servingSize || 'Unknown',
      servingsPerContainer: data.servingsPerContainer || 1,
      verified: data.verified || false,
      thirdPartyTested: data.thirdPartyTested || false,
      certifications: data.certifications || [],
      createdAt: data.createdAt || new Date().toISOString(),
      updatedAt: data.updatedAt || new Date().toISOString(),
    };
  }

  async saveProduct(product: Product): Promise<void> {
    try {
      const { error } = await supabase.from('products').upsert({
        id: product.id,
        name: product.name,
        brand: product.brand,
        barcode: product.barcode,
        ingredients: product.ingredients,
        nutrients: product.nutrients,
        source: product.source,
        source_id: product.sourceId,
        category: product.category,
        servingSize: product.servingSize,
        servingsPerContainer: product.servingsPerContainer,
        verified: product.verified,
        thirdPartyTested: product.thirdPartyTested,
        certifications: product.certifications,
        createdAt: product.createdAt,
        updatedAt: product.updatedAt,
      });
      if (error) {
        console.error('Supabase save product error:', error);
      }
    } catch (e) {
      console.error('Supabase save product error:', e);
    }
  }

  async calculateNutrientAccumulation(products: Product[]): Promise<NutrientRDI[]> {
    const nutrientMap: { [key: string]: NutrientRDI } = {};

    for (const product of products) {
      for (const nutrient of product.nutrients) {
        if (!nutrientMap[nutrient.name]) {
          nutrientMap[nutrient.name] = {
            name: nutrient.name,
            totalAmount: 0,
            unit: nutrient.unit,
            percentRDI: 0,
            upperLimit: NUTRIENT_LIMITS[nutrient.name]?.ul || Infinity,
            nutrientWarnings: [],
          };
        }
        nutrientMap[nutrient.name].totalAmount += nutrient.amount;
        nutrientMap[nutrient.name].percentRDI += nutrient.percentDV || 0;
        if (NUTRIENT_LIMITS[nutrient.name] && nutrientMap[nutrient.name].totalAmount > NUTRIENT_LIMITS[nutrient.name].ul) {
          nutrientMap[nutrient.name].nutrientWarnings.push({
            nutrient: nutrient.name,
            currentTotal: nutrientMap[nutrient.name].totalAmount,
            unit: nutrient.unit,
            percentOfLimit: (nutrientMap[nutrient.name].totalAmount / NUTRIENT_LIMITS[nutrient.name].ul) * 100,
            recommendation: `Reduce intake of ${nutrient.name} to avoid exceeding upper limit`,
          });
        }
      }
    }

    return Object.values(nutrientMap).map(nutrient => ({
      ...nutrient,
      exceedsLimit: nutrient.totalAmount > nutrient.upperLimit,
    }));
  }

  private mapDSLDCategory(productType: string): string {
    if (!productType) return 'supplement';
    const type = productType.toLowerCase();
    if (type.includes('vitamin')) return 'vitamin';
    if (type.includes('mineral')) return 'mineral';
    if (type.includes('amino') || type.includes('protein')) return 'amino_acid';
    if (type.includes('botanical')) return 'herbal';
    if (type.includes('omega') || type.includes('fatty')) return 'omega3';
    if (type.includes('multi')) return 'multivitamin';
    return 'supplement';
  }

  private extractServingSize(servingSizes: any): string {
    if (Array.isArray(servingSizes) && servingSizes.length > 0) {
      const serving = servingSizes[0];
      const quantity = serving.minQuantity || serving.maxQuantity || 1;
      const unit = serving.unit || 'serving';
      return `${quantity} ${unit}`;
    }
    return '1 serving';
  }
}

export const multiSourceDataService = new MultiSourceDataService();