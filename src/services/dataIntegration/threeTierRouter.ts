/// src/services/dataIntegration/threeTierRouter.ts
import { supabase } from '../supabase/client';
import { Product, DatabaseSourceReference, InteractionRule, SupplementInteraction, NutrientRDI, UserStack, UserProfile, Interaction } from '../../types';
import { CRITICAL_INTERACTIONS, SUPPLEMENT_INTERACTIONS, APP_CONFIG } from '../../constants/index';
import { multiSourceDataService } from './multiSourceDataService';

interface ThreeTierResult {
  result: any;
  tier: 'RULE_BASED' | 'CACHE' | 'API';
  responseTime: number;
}

export class ThreeTierRouter {
  async checkInteractions(products: Product[], stack: UserStack[]): Promise<Interaction[]> {
    const startTime = Date.now();
    const interactions: Interaction[] = [];

    // Tier 1: Local rules
    for (const product of products) {
      const productIngredients = product.ingredients.map(ing => ing.name.toLowerCase());
      
      // Check medication interactions
      for (const [medication, rule] of Object.entries(CRITICAL_INTERACTIONS)) {
        if (productIngredients.some(ing => rule.supplements.map(s => s.toLowerCase()).includes(ing))) {
          interactions.push({
            id: `critical_${medication}_${productIngredients.find(ing => rule.supplements.map(s => s.toLowerCase()).includes(ing))}`,
            type: 'supplement-medication',
            severity: rule.severity,
            substance1: { name: medication, category: 'medication', commonNames: [medication] },
            substance2: { name: productIngredients.find(ing => rule.supplements.map(s => s.toLowerCase()).includes(ing))!, category: 'supplement', commonNames: [] },
            mechanism: rule.mechanism,
            description: rule.evidence,
            clinicalSignificance: rule.evidence,
            evidence: {
              level: 'A',
              sources: rule.sources.map(s => s.url),
              studyCount: rule.sources.length,
              lastUpdated: new Date().toISOString(),
            },
            recommendations: {
              action: rule.management.includes('consult') ? 'consult_provider' : 'monitor',
              monitoring: [rule.management],
            },
          });
        }
      }
      
      // Check supplement interactions
      for (const [supplement, rule] of Object.entries(SUPPLEMENT_INTERACTIONS)) {
        if (productIngredients.includes(supplement.toLowerCase())) {
          for (const stackItem of stack.flatMap(s => s.ingredients?.map(ing => ing.name) || [s.name])) {
            if (rule.conflicting_supplements?.includes(stackItem) || rule.synergistic_supplements?.includes(stackItem)) {
              interactions.push({
                id: `supplement_${supplement}_${stackItem}`,
                type: 'supplement-supplement',
                severity: rule.severity,
                substance1: { name: supplement, category: 'supplement', commonNames: [supplement] },
                substance2: { name: stackItem, category: 'supplement', commonNames: [stackItem] },
                mechanism: rule.mechanism,
                description: rule.evidence,
                clinicalSignificance: rule.evidence,
                evidence: {
                  level: 'A',
                  sources: rule.sources.map(s => s.url),
                  studyCount: rule.sources.length,
                  lastUpdated: new Date().toISOString(),
                },
                recommendations: {
                  action: rule.management.includes('consult') ? 'consult_provider' : 'monitor',
                  monitoring: [rule.management],
                },
              });
            }
          }
        }
      }

      // Tier 1: Database rules
      const { data: rules } = await supabase
        .from('critical_interaction_rules')
        .select('*')
        .eq('active', true);
      for (const ing of productIngredients) {
        for (const rule of rules || []) {
          if (
            ing.toLowerCase().includes(rule.item1_identifier.toLowerCase()) &&
            stack.some(s => s.name.toLowerCase().includes(rule.item2_identifier.toLowerCase()))
          ) {
            interactions.push({
              id: `db_${rule.item1_identifier}_${rule.item2_identifier}`,
              type: 'supplement-medication',
              severity: rule.severity.toLowerCase() as InteractionSeverity,
              substance1: { name: rule.item1_identifier, category: 'supplement', commonNames: [rule.item1_identifier] },
              substance2: { name: rule.item2_identifier, category: 'medication', commonNames: [rule.item2_identifier] },
              mechanism: rule.mechanism,
              description: rule.clinicalSignificance,
              clinicalSignificance: rule.clinicalSignificance,
              evidence: {
                level: rule.evidenceLevel,
                sources: rule.sources.map((s: any) => s.sourceName),
                studyCount: rule.studyCount || 0,
                lastUpdated: new Date().toISOString(),
              },
              recommendations: {
                action: rule.recommendation as any,
                monitoring: rule.monitoring || [],
              },
            });
          }
        }
      }
    }

    await this.logAnalysis({ result: { stackInteraction: { interactions } }, tier: 'RULE_BASED', responseTime: Date.now() - startTime }, 'session_' + Date.now());
    return interactions;
  }

  async getProductByBarcode(barcode: string): Promise<Product | null> {
    const startTime = Date.now();

    // Tier 1: Local rules (not implemented)
    
    // Tier 2: Cache via multiSourceDataService
    const cachedProduct = await multiSourceDataService.fetchProductByBarcode(barcode);
    if (cachedProduct) {
      await this.logAnalysis({ result: cachedProduct, tier: 'CACHE', responseTime: Date.now() - startTime }, 'session_' + barcode);
      return cachedProduct;
    }

    // Tier 3: Live API query
    const product = await multiSourceDataService.fetchProductByBarcode(barcode);
    if (product) {
      await multiSourceDataService.saveProduct(product);
      await this.logAnalysis({ result: product, tier: 'API', responseTime: Date.now() - startTime }, 'session_' + barcode);
    }
    return product;
  }

  async searchProductsByName(name: string): Promise<Product[]> {
    const startTime = Date.now();

    // Tier 1: Local rules (not implemented)
    
    // Tier 2: Cache
    const cachedProducts = await multiSourceDataService.fetchProductsByName(name);
    if (cachedProducts.length > 0) {
      await this.logAnalysis({ result: cachedProducts, tier: 'CACHE', responseTime: Date.now() - startTime }, 'session_' + name);
      return cachedProducts;
    }

    // Tier 3: Live API query
    const products = await multiSourceDataService.fetchProductsByName(name);
    for (const product of products) {
      await multiSourceDataService.saveProduct(product);
    }
    await this.logAnalysis({ result: products, tier: 'API', responseTime: Date.now() - startTime }, 'session_' + name);
    return products;
  }

  async getDrugInteractions(drugName: string): Promise<string[]> {
    const startTime = Date.now();

    // Tier 1: Local rules
    const localInteractions = CRITICAL_INTERACTIONS[drugName.toLowerCase()];
    if (localInteractions) {
      await this.logAnalysis({ result: localInteractions.supplements.concat(localInteractions.medications), tier: 'RULE_BASED', responseTime: Date.now() - startTime }, 'session_' + drugName);
      return localInteractions.supplements.concat(localInteractions.medications);
    }

    // Tier 2/Tier 3: Cache and API
    const interactions = await multiSourceDataService.fetchDrugInteractions(drugName);
    await this.logAnalysis({ result: interactions, tier: interactions.length > 0 ? 'CACHE' : 'API', responseTime: Date.now() - startTime }, 'session_' + drugName);
    return interactions;
  }

  async getMedicalEvidence(query: string): Promise<DatabaseSourceReference[]> {
    const startTime = Date.now();

    // Tier 1: Local rules (not implemented)
    
    // Tier 2/Tier 3: Cache and API
    const evidence = await multiSourceDataService.searchPubMedEvidence(query);
    await this.logAnalysis({ result: evidence, tier: evidence.length > 0 ? 'CACHE' : 'API', responseTime: Date.now() - startTime }, 'session_' + query);
    return evidence;
  }

  async calculateNutrientAccumulation(products: Product[]): Promise<NutrientRDI[]> {
    const startTime = Date.now();

    // Tier 1: Local rules (not implemented)
    
    // Tier 2/Tier 3: Cache and calculation
    const nutrients = await multiSourceDataService.calculateNutrientAccumulation(products);
    await this.logAnalysis({ result: nutrients, tier: 'API', responseTime: Date.now() - startTime }, 'session_nutrients_' + Date.now());
    return nutrients;
  }

  private async logAnalysis(analysis: ThreeTierResult, sessionId: string) {
    try {
      await supabase.from('interaction_check_logs').insert({
        session_id: sessionId,
        items_checked: analysis.result.stackInteraction || analysis.result,
        interaction_count: analysis.result.stackInteraction?.interactions.length || 0,
        critical_count: analysis.result.stackInteraction?.interactions.filter((i: Interaction) => i.severity === 'CRITICAL').length || 0,
        high_count: analysis.result.stackInteraction?.interactions.filter((i: Interaction) => i.severity === 'HIGH').length || 0,
        moderate_count: analysis.result.stackInteraction?.interactions.filter((i: Interaction) => i.severity === 'MODERATE').length || 0,
        low_count: analysis.result.stackInteraction?.interactions.filter((i: Interaction) => i.severity === 'LOW').length || 0,
        response_time_ms: analysis.responseTime,
        tier_used: analysis.tier,
      });
    } catch (error) {
      console.error('Error logging analysis:', error);
    }
  }
}

export const threeTierRouter = new ThreeTierRouter();