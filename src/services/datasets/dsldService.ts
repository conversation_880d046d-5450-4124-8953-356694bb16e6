// src/services/datasets/dsldService.ts
import { supabase } from '../supabase/client';

interface DSLDProduct {
  dsld_id: string;
  product_name: string;
  brand_name: string;
  ingredients: { name: string; amount?: string; unit?: string }[];
}

export class DSLDService {
  async searchDSLD(searchTerm: string, brand?: string): Promise<DSLDProduct[]> {
    try {
      let query = supabase.from('dsld_products').select('*').ilike('product_name', `%${searchTerm}%`);
      if (brand) query = query.ilike('brand_name', `%${brand}%`);
      const { data, error } = await query.limit(10);
      if (error) throw error;
      return data.map(item => ({
        dsld_id: item.dsld_id,
        product_name: item.product_name,
        brand_name: item.brand_name,
        ingredients: item.supplement_facts || [],
      }));
    } catch (error) {
      console.error('DSLD Supabase search error:', error);
      return [];
    }
  }
}

export const dsldService = new DSLDService();