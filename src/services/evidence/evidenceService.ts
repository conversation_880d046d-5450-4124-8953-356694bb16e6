// src/services/evidence/evidenceService.ts
import { multiSourceDataService } from '../dataIntegration/multiSourceDataService';
import { cacheManager } from '../dataIntegration/cacheManager';

interface PubMedEvidence {
  pmid: string;
  title: string;
  authors: string;
  journal: string;
  pubDate: string;
  abstract: string;
  evidenceType: string;
  studySize: number | null;
}

export class EvidenceService {
  async getEvidenceForIngredient(ingredient: string, condition?: string): Promise<PubMedEvidence[]> {
    return multiSourceDataService.searchPubMedEvidence(ingredient, condition);
  }

  determineEvidenceGrade(studies: PubMedEvidence[]): 'A' | 'B' | 'C' | 'D' | 'F' {
    const metaCount = studies.filter(s => s.evidenceType === 'meta-analysis').length;
    const rctCount = studies.filter(s => s.evidenceType === 'rct').length;
    const totalCount = studies.length;

    if (metaCount >= 3) return 'A';
    if (metaCount >= 1 || rctCount >= 5) return 'B';
    if (rctCount >= 2 || totalCount >= 10) return 'C';
    if (totalCount >= 3) return 'D';
    return 'F';
  }

  calculateEfficacyScore(studies: PubMedEvidence[]): number {
    let score = 50;
    studies.forEach(study => {
      if (study.evidenceType === 'meta-analysis') score += 15;
      else if (study.evidenceType === 'rct') score += 10;
      else if (study.evidenceType === 'clinical-trial') score += 7;
      else score += 3;
    });
    const largeStudies = studies.filter(s => s.studySize && s.studySize > 500);
    score += largeStudies.length * 5;
    return Math.min(100, score);
  }
}

export const evidenceService = new EvidenceService();