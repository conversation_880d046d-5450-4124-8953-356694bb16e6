// src/services/gamification/gamificationService.ts
import { storageAdapter } from '../storage/storageAdapter';
import { supabase } from '../supabase/client';
// Removed specific GamificationPoint, GamificationAchievementId, UserStack imports from constants,
// as the entire GAMIFICATION object is imported. UserStack is not used here.
import { STORAGE_KEYS, APP_CONFIG, GAMIFICATION } from '../../constants/index';

// Define the interface for the gamification progress object
interface GamificationProgress {
  points: number;
  level: keyof typeof GAMIFICATION.LEVELS;
  achievements: (keyof typeof GAMIFICATION.ACHIEVEMENTS)[]; // Specific achievement IDs
  streak: {
    current: number;
    lastScanDate: string | null;
    longest: number;
  };
  statistics: {
    totalScans: number;
    interactionsFound: number;
    safeProducts: number;
    stackItems: number;
    aiConsultations: number;
  };
}

interface StreakResult {
  streakContinued: boolean;
  streakBroken: boolean;
  pointsAwarded: number;
}

interface AchievementResult {
  achievementsUnlocked: (keyof typeof GAMIFICATION.ACHIEVEMENTS)[];
  pointsAwarded: number;
}

export class GamificationService {
  private static instance: GamificationService;
  private userId: string | null = null;

  // Callbacks for UI updates
  public onLevelUp?: (newLevel: keyof typeof GAMIFICATION.LEVELS) => void;
  public onAchievementUnlocked?: (achievementId: keyof typeof GAMIFICATION.ACHIEVEMENTS, points: number) => void;

  private constructor() {}

  public static getInstance(): GamificationService {
    if (!GamificationService.instance) {
      GamificationService.instance = new GamificationService();
    }
    return GamificationService.instance;
  }

  /**
   * Initializes the gamification service for a given user.
   * Fetches existing progress or initializes new progress.
   * @param userId The ID of the current authenticated user.
   */
  public async initialize(userId: string): Promise<void> {
    this.userId = userId;
    await storageAdapter.initialize(); // Ensure storage is ready
    const progress = await this.getUserProgress();
    if (!progress) {
      await this.initializeUserProgress();
    }
  }

  /**
   * Awards points for a specific action and updates user progress.
   * Also triggers level-up checks and achievement checks.
   * @param action The type of gamification action.
   * @param metadata Additional metadata for the analytics log.
   */
  public async awardPoints(
    action: keyof typeof GAMIFICATION.POINTS,
    metadata: Record<string, any> = {}
  ): Promise<void> {
    if (!this.userId) {
      console.warn('GamificationService not initialized or user is anonymous. Points not awarded.');
      return; // Or throw an error if this state should be impossible
    }

    const points = GAMIFICATION.POINTS[action] || 0;
    if (points <= 0) return;

    let progress = await this.getUserProgress();
    if (!progress) {
      await this.initializeUserProgress();
      progress = await this.getUserProgress(); // Re-fetch after initialization
      if (!progress) throw new Error('Failed to initialize user progress.');
    }

    progress.points += points;

    // Update statistics based on the action
    progress.statistics = {
      ...progress.statistics,
      totalScans: (action === 'DAILY_SCAN' || action === 'FIRST_SCAN') ? progress.statistics.totalScans + 1 : progress.statistics.totalScans,
      interactionsFound: (action === 'INTERACTION_FOUND') ? progress.statistics.interactionsFound + 1 : progress.statistics.interactionsFound,
      safeProducts: (action === 'SAFE_PRODUCT') ? progress.statistics.safeProducts + 1 : progress.statistics.safeProducts,
      stackItems: ['STACK_ADD', 'STACK_REMOVE', 'STACK_UPDATE'].includes(action)
        ? action === 'STACK_ADD' ? progress.statistics.stackItems + 1
        : action === 'STACK_REMOVE' ? Math.max(0, progress.statistics.stackItems - 1)
        : progress.statistics.stackItems
        : progress.statistics.stackItems,
      aiConsultations: (action === 'AI_CONSULTATION') ? progress.statistics.aiConsultations + 1 : progress.statistics.aiConsultations,
    };

    const oldLevel = progress.level;
    const newLevel = this.calculateLevel(progress.points);
    if (newLevel !== oldLevel) {
      progress.level = newLevel;
      progress.points += GAMIFICATION.POINTS.LEVEL_UP; // Award bonus for leveling up
      this.onLevelUp?.(newLevel); // Trigger level up event
      await this.logAnalyticsEvent('level_up', { level: newLevel, previousLevel: oldLevel });
    }

    await this.saveUserProgress(progress);
    await this.logAnalyticsEvent(action, { pointsAwarded: points, ...metadata });

    const achievementResult = await this.checkAchievements();
    if (achievementResult.achievementsUnlocked.length > 0) {
      progress.points += achievementResult.pointsAwarded;
      progress.achievements.push(...achievementResult.achievementsUnlocked); // Add new achievements
      await this.saveUserProgress(progress);
      // Trigger achievement unlocked event for each
      achievementResult.achievementsUnlocked.forEach(achId => {
        const achPoints = GAMIFICATION.ACHIEVEMENTS[achId]?.points || 0;
        this.onAchievementUnlocked?.(achId, achPoints);
      });
    }
  }

  /**
   * Updates the user's streak based on daily activity.
   */
  public async updateStreak(): Promise<StreakResult> {
    if (!this.userId) {
      console.warn('GamificationService not initialized or user is anonymous. Streak not updated.');
      return { streakContinued: false, streakBroken: false, pointsAwarded: 0 };
    }

    let progress = await this.getUserProgress();
    if (!progress) {
      await this.initializeUserProgress();
      progress = await this.getUserProgress();
      if (!progress) throw new Error('Failed to retrieve user progress for streak update.');
    }

    const today = new Date().toISOString().split('T')[0];
    const lastScan = progress.streak.lastScanDate;

    // Calculate yesterday's date string
    const yesterdayDate = new Date();
    yesterdayDate.setDate(yesterdayDate.getDate() - 1);
    const yesterday = yesterdayDate.toISOString().split('T')[0];

    let streakContinued = false;
    let streakBroken = false;
    let pointsAwarded = 0;

    if (lastScan === today) {
      // Already updated today, no change to streak
      return { streakContinued: false, streakBroken: false, pointsAwarded: 0 };
    } else if (lastScan === yesterday) {
      // Streak continues
      progress.streak.current += 1;
      streakContinued = true;
      // Calculate bonus points
      let bonusMultiplier = 1;
      if (progress.streak.current >= 100) bonusMultiplier = GAMIFICATION.STREAK_BONUS_MULTIPLIER[100];
      else if (progress.streak.current >= 30) bonusMultiplier = GAMIFICATION.STREAK_BONUS_MULTIPLIER[30];
      else if (progress.streak.current >= 7) bonusMultiplier = GAMIFICATION.STREAK_BONUS_MULTIPLIER[7];
      pointsAwarded = GAMIFICATION.POINTS.STREAK_BONUS * bonusMultiplier;
      progress.points += pointsAwarded;
    } else {
      // Streak broken or new streak starts
      streakBroken = lastScan !== null; // Only broken if there was a previous scan
      progress.streak.current = 1;
      pointsAwarded = GAMIFICATION.POINTS.STREAK_BONUS; // Award base streak points for starting new streak
      progress.points += pointsAwarded;
    }

    progress.streak.lastScanDate = today;
    progress.streak.longest = Math.max(progress.streak.current, progress.streak.longest);

    await this.saveUserProgress(progress);
    await this.logAnalyticsEvent('streak_updated', {
      streak: progress.streak.current,
      longestStreak: progress.streak.longest,
      streakContinued,
      streakBroken,
      pointsAwarded,
    });

    const achievementResult = await this.checkAchievements();
    if (achievementResult.achievementsUnlocked.length > 0) {
      progress.points += achievementResult.pointsAwarded;
      progress.achievements.push(...achievementResult.achievementsUnlocked);
      await this.saveUserProgress(progress);
      achievementResult.achievementsUnlocked.forEach(achId => {
        const achPoints = GAMIFICATION.ACHIEVEMENTS[achId]?.points || 0;
        this.onAchievementUnlocked?.(achId, achPoints);
      });
    }

    return { streakContinued, streakBroken, pointsAwarded };
  }

  /**
   * Fetches the current gamification progress for the user.
   * Uses local storage if `HIPAA_LOCAL_ONLY` is true, otherwise fetches from Supabase.
   */
  public async getUserProgress(): Promise<GamificationProgress | null> {
    if (!this.userId) return null;

    if (APP_CONFIG.HIPAA_LOCAL_ONLY) {
      const progress = await storageAdapter.getItem(`${STORAGE_KEYS.GAMIFICATION_PREFIX}${this.userId}`);
      return progress ? JSON.parse(progress) : null;
    }

    try {
      const { data, error } = await supabase
        .from('user_gamification')
        .select('points, level, achievements, streak, statistics') // Select specific columns matching GamificationProgress
        .eq('user_id', this.userId)
        .single();

      if (error) {
        // If row not found (e.g., first time), return null for initialization
        if (error.code === 'PGRST116') { // No rows found
            return null;
        }
        console.error('Error fetching gamification progress from Supabase:', error);
        throw new Error(`Failed to fetch gamification progress: ${error.message}`);
      }

      // Ensure streak and statistics are properly typed after fetching from DB
      return {
        points: data.points,
        level: data.level as keyof typeof GAMIFICATION.LEVELS,
        achievements: data.achievements as (keyof typeof GAMIFICATION.ACHIEVEMENTS)[],
        streak: data.streak,
        statistics: data.statistics,
      };
    } catch (e) {
      console.error('Supabase get user progress error:', e);
      return null;
    }
  }

  /**
   * Initializes user gamification progress with default values.
   * Stores locally or remotely based on `HIPAA_LOCAL_ONLY`.
   */
  private async initializeUserProgress(): Promise<void> {
    if (!this.userId) throw new Error('GamificationService not initialized');

    const initialProgress: GamificationProgress = {
      points: 0,
      level: 'BEGINNER',
      achievements: [],
      streak: { current: 0, lastScanDate: null, longest: 0 },
      statistics: {
        totalScans: 0,
        interactionsFound: 0,
        safeProducts: 0,
        stackItems: 0,
        aiConsultations: 0,
      },
    };

    try {
        if (APP_CONFIG.HIPAA_LOCAL_ONLY) {
            await storageAdapter.setItem(
                `${STORAGE_KEYS.GAMIFICATION_PREFIX}${this.userId}`,
                JSON.stringify(initialProgress)
            );
        } else {
            // Supabase upsert will create if not exists
            await supabase.from('user_gamification').upsert({
                user_id: this.userId,
                points: initialProgress.points,
                level: initialProgress.level,
                achievements: initialProgress.achievements,
                streak: initialProgress.streak,
                statistics: initialProgress.statistics,
            }, { onConflict: 'user_id' }); // Use onConflict for upsert behavior
        }
    } catch (e) {
        console.error('Error initializing user progress:', e);
        throw new Error(`Failed to initialize user progress: ${e instanceof Error ? e.message : String(e)}`);
    }
  }

  /**
   * Saves the current gamification progress.
   * Stores locally or remotely based on `HIPAA_LOCAL_ONLY`.
   * @param progress The GamificationProgress object to save.
   */
  private async saveUserProgress(progress: GamificationProgress): Promise<void> {
    if (!this.userId) throw new Error('GamificationService not initialized');

    try {
      if (APP_CONFIG.HIPAA_LOCAL_ONLY) {
        await storageAdapter.setItem(
          `${STORAGE_KEYS.GAMIFICATION_PREFIX}${this.userId}`,
          JSON.stringify(progress)
        );
      } else {
        await supabase
          .from('user_gamification')
          .upsert({
            user_id: this.userId,
            points: progress.points,
            level: progress.level,
            achievements: progress.achievements,
            streak: progress.streak,
            statistics: progress.statistics,
          }, { onConflict: 'user_id' }); // Ensures updates or inserts
      }

      await this.logAnalyticsEvent('progress_updated', {
        points: progress.points,
        level: progress.level,
        achievementsCount: progress.achievements.length,
      });
    } catch (e) {
      console.error('Error saving user progress:', e);
      throw new Error(`Failed to save user progress: ${e instanceof Error ? e.message : String(e)}`);
    }
  }

  /**
   * Checks for unlocked achievements based on current progress and awards points.
   * @returns An object containing newly unlocked achievements and total points awarded.
   */
  private async checkAchievements(): Promise<AchievementResult> {
    if (!this.userId) throw new Error('GamificationService not initialized');

    const progress = await this.getUserProgress();
    if (!progress) throw new Error('User progress not found for achievement check.');

    const thresholds = APP_CONFIG.ACHIEVEMENT_THRESHOLDS;
    const achievementsUnlocked: (keyof typeof GAMIFICATION.ACHIEVEMENTS)[] = [];
    let pointsAwarded = 0;

    const hasPrerequisite = (achievementId: keyof typeof GAMIFICATION.ACHIEVEMENTS) => {
      const prereqs = GAMIFICATION.ACHIEVEMENTS[achievementId].prerequisites;
      return !prereqs || prereqs.every(prereq => progress.achievements.includes(prereq));
    };

    // Define achievements to check in a consistent order (matching `constants/index.ts` keys)
    const allAchievementKeys: (keyof typeof GAMIFICATION.ACHIEVEMENTS)[] = Object.keys(GAMIFICATION.ACHIEVEMENTS) as (keyof typeof GAMIFICATION.ACHIEVEMENTS)[];

    for (const achId of allAchievementKeys) {
        if (!progress.achievements.includes(achId) && hasPrerequisite(achId)) {
            const achievement = GAMIFICATION.ACHIEVEMENTS[achId];

            let unlocked = false;
            switch (achId) {
                case 'FIRST_STEPS':
                    unlocked = progress.statistics.totalScans >= thresholds.FIRST_10_SCANS;
                    break;
                case 'STREAK_MASTER':
                    unlocked = progress.streak.current >= thresholds.CENTURY_LEGEND; // Using CENTURY_LEGEND for STREAK_MASTER
                    break;
                case 'SAFETY_FIRST':
                    unlocked = progress.statistics.interactionsFound >= thresholds.SAFETY_GUARDIAN;
                    break;
                case 'STACK_BUILDER':
                    unlocked = progress.statistics.stackItems >= thresholds.STACK_BUILDER;
                    break;
                case 'OPTIMIZER':
                    unlocked = progress.statistics.interactionsFound > 0 && progress.statistics.stackItems > 0;
                    break;
                case 'KNOWLEDGE_SEEKER':
                    unlocked = progress.statistics.aiConsultations >= thresholds.KNOWLEDGE_SEEKER;
                    break;
                case 'CONTRIBUTOR':
                    // This achievement's description is 'Identify 5 safe products'.
                    // You might need a specific statistic for 'products submitted' or 'products verified'.
                    // For now, using 'safeProducts' as a proxy if 'submissionsApproved' is not in stats.
                    unlocked = progress.statistics.safeProducts >= thresholds.CONTRIBUTOR;
                    break;
                case 'SHARER':
                    // Assuming 'SHARER' statistic is tracked, or use other proxy.
                    // If 'share count' isn't explicitly in statistics, this will need a specific metadata check or new stat.
                    // For now, using totalScans as a placeholder for share-related activity if no direct stat.
                    unlocked = progress.statistics.totalScans >= thresholds.SHARER; // This might be a placeholder
                    break;
                // Add checks for other dynamic achievements from APP_CONFIG.ACHIEVEMENT_THRESHOLDS if they are defined
                // For example, if you have 'LEVEL_5' and 'LEVEL_10' in ACHIEVEMENT_THRESHOLDS:
                // case 'LEVEL_5': unlocked = progress.level >= thresholds.LEVEL_5; break;
                // case 'LEVEL_10': unlocked = progress.level >= thresholds.LEVEL_10; break;
                // case 'WEEK_WARRIOR': unlocked = progress.streak.current >= thresholds.WEEK_WARRIOR; break;
                // case 'MONTHLY_CHAMPION': unlocked = progress.streak.current >= thresholds.MONTHLY_CHAMPION; break;
                // case 'CENTURY_LEGEND': unlocked = progress.streak.current >= thresholds.CENTURY_LEGEND; break;
            }

            if (unlocked) {
                achievementsUnlocked.push(achId);
                pointsAwarded += achievement.points;
            }
        }
    }

    return { achievementsUnlocked, pointsAwarded };
  }


  /**
   * Calculates the user's current gamification level based on their points.
   * @param points The user's total points.
   * @returns The corresponding level ID.
   */
  private calculateLevel(points: number): keyof typeof GAMIFICATION.LEVELS {
    // Iterate through levels to find the highest applicable level based on points.
    // Assuming GAMIFICATION.LEVELS is ordered by min points.
    let currentLevel: keyof typeof GAMIFICATION.LEVELS = 'BEGINNER';
    for (const levelId of Object.keys(GAMIFICATION.LEVELS) as (keyof typeof GAMIFICATION.LEVELS)[]) {
        const levelConfig = GAMIFICATION.LEVELS[levelId];
        if (points >= levelConfig.min) {
            currentLevel = levelId;
        } else {
            // Since levels are ordered, if points are less than current level's min,
            // then it belongs to the previous level.
            break;
        }
    }
    return currentLevel;
  }

  /**
   * Logs an analytics event to Supabase.
   * @param action The action performed.
   * @param metadata Additional data for the event.
   */
  private async logAnalyticsEvent(
    action: string,
    metadata: Record<string, any>
  ): Promise<void> {
    if (!this.userId || APP_CONFIG.HIPAA_LOCAL_ONLY) {
      // Don't log to Supabase if no user ID or if HIPAA_LOCAL_ONLY is enabled
      console.log(`Analytics event (local-only/anonymous): ${action}`, metadata);
      return;
    }

    try {
      await supabase.from('analytics_events').insert({
        user_id: this.userId,
        action,
        metadata: {
          ...metadata,
          timestamp: new Date().toISOString(),
        },
      });
    } catch (error) {
      console.error('Error logging analytics event to Supabase:', error);
    }
  }

  /**
   * Resets a user's gamification progress to default values.
   */
  public async resetProgress(): Promise<void> {
    if (!this.userId) throw new Error('GamificationService not initialized for reset.');
    await this.initializeUserProgress();
  }
}

export const gamificationService = GamificationService.getInstance();