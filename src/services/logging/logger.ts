// src/services/logging/logger.ts
import { APP_CONFIG } from '../../constants';

export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  context?: string;
  data?: any;
}

class Logger {
  private isDevelopment = __DEV__;
  private maxLogEntries = 1000;
  private logEntries: LogEntry[] = [];

  private createLogEntry(level: LogLevel, message: string, context?: string, data?: any): LogEntry {
    return {
      timestamp: new Date().toISOString(),
      level,
      message,
      context,
      data,
    };
  }

  private addLogEntry(entry: LogEntry): void {
    this.logEntries.push(entry);
    
    // Keep only the most recent entries
    if (this.logEntries.length > this.maxLogEntries) {
      this.logEntries = this.logEntries.slice(-this.maxLogEntries);
    }
  }

  private shouldLog(level: LogLevel): boolean {
    // In production, only log warnings and errors
    if (!this.isDevelopment) {
      return level === 'warn' || level === 'error';
    }
    return true;
  }

  debug(message: string, context?: string, data?: any): void {
    if (!this.shouldLog('debug')) return;

    const entry = this.createLogEntry('debug', message, context, data);
    this.addLogEntry(entry);

    if (this.isDevelopment) {
      console.log(`[DEBUG] ${context ? `[${context}] ` : ''}${message}`, data || '');
    }
  }

  info(message: string, context?: string, data?: any): void {
    if (!this.shouldLog('info')) return;

    const entry = this.createLogEntry('info', message, context, data);
    this.addLogEntry(entry);

    if (this.isDevelopment) {
      console.log(`[INFO] ${context ? `[${context}] ` : ''}${message}`, data || '');
    }
  }

  warn(message: string, context?: string, data?: any): void {
    if (!this.shouldLog('warn')) return;

    const entry = this.createLogEntry('warn', message, context, data);
    this.addLogEntry(entry);

    console.warn(`[WARN] ${context ? `[${context}] ` : ''}${message}`, data || '');
  }

  error(message: string, context?: string, error?: any): void {
    if (!this.shouldLog('error')) return;

    const entry = this.createLogEntry('error', message, context, error);
    this.addLogEntry(entry);

    console.error(`[ERROR] ${context ? `[${context}] ` : ''}${message}`, error || '');

    // In production, you might want to send critical errors to a monitoring service
    if (!this.isDevelopment && this.shouldReportError(error)) {
      this.reportError(message, context, error);
    }
  }

  private shouldReportError(error: any): boolean {
    // Don't report network errors or user cancellations
    if (error?.code === 'NETWORK_ERROR' || error?.name === 'AbortError') {
      return false;
    }
    return true;
  }

  private reportError(message: string, context?: string, error?: any): void {
    // In a real app, this would send to Sentry, Crashlytics, etc.
    // For now, we'll just ensure it's logged to console
    console.error('CRITICAL ERROR:', { message, context, error });
  }

  // Get recent logs for debugging
  getRecentLogs(count: number = 50): LogEntry[] {
    return this.logEntries.slice(-count);
  }

  // Clear all logs
  clearLogs(): void {
    this.logEntries = [];
  }

  // Export logs for debugging
  exportLogs(): string {
    return JSON.stringify(this.logEntries, null, 2);
  }
}

// Export singleton instance
export const logger = new Logger();

// Convenience methods for common use cases
export const logInfo = (message: string, context?: string, data?: any) => logger.info(message, context, data);
export const logWarn = (message: string, context?: string, data?: any) => logger.warn(message, context, data);
export const logError = (message: string, context?: string, error?: any) => logger.error(message, context, error);
export const logDebug = (message: string, context?: string, data?: any) => logger.debug(message, context, data);