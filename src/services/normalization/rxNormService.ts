// src/services/normalization/rxNormService.ts
import axios from 'axios';
import { cacheManager } from '../dataIntegration/cacheManager';

// Define interface for RxNorm data
export interface RxNormConcept {
  rxcui: string;
  name: string;
  synonym?: string;
  tty: string;
}

// RxNormService normalizes ingredient names using the RxNorm API
export class RxNormService {
  private client = axios.create({
    baseURL: 'https://rxnav.nlm.nih.gov/REST', // Fixed URL as per RxNorm standard
  });

  // Normalize ingredient name with caching
  async normalizeIngredientName(ingredientName: string): Promise<RxNormConcept | null> {
    const cacheKey = `rxnorm_${ingredientName.toLowerCase()}`;
    return cacheManager.getCachedOrFetch(cacheKey, async () => {
      try {
        const response = await this.client.get('/rxcui.json', {
          params: { name: ingredientName, search: 'approximate' },
        });
        const rxcui = response.data.idGroup?.rxnormId?.[0];
        if (!rxcui) return null;

        const conceptResponse = await this.client.get(`/rxcui/${rxcui}/allProperties.json`, {
          params: { prop: 'names' },
        });
        const properties = conceptResponse.data.propConceptGroup?.propConcept || [];
        const nameProp = properties.find((p: any) => p.propName === 'RxNorm Name');
        const synonymProp = properties.find((p: any) => p.propName === 'SYNONYM');

        return {
          rxcui,
          name: nameProp?.propValue || ingredientName,
          synonym: synonymProp?.propValue,
          tty: properties.find((p: any) => p.propName === 'TERM_TYPE')?.propValue || 'IN',
        };
      } catch (error) {
        console.error('RxNorm normalization error:', error);
        return null;
      }
    }, { ttl: 30 * 24 * 60 * 60 * 1000 }); // Cache for 30 days
  }
}

// Export singleton instance for use throughout the app
export const rxNormService = new RxNormService();