// src/services/products/index.ts
import { huggingfaceService } from '../ai/huggingface';
import { interactionService } from '../interactions';
import { scanRateLimiter } from '../../utils/rateLimiting';
import { multiSourceDataService } from '../dataIntegration/multiSourceDataService';
import { aiCache } from '../ai/AICache';
import { convertSearchResultToProduct } from '../search/productConverter';
import type {
  Product,
  ProductAnalysis,
  UserStack,
  SearchResult,
} from '../../types';
import { APP_CONFIG } from '../../constants/index';

class ProductServiceCircuitBreaker {
  private failures = 0;
  private lastFailTime = 0;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';
  private readonly failureThreshold = 5;
  private readonly resetTimeout = 60000;

  async execute<T>(
    primaryFn: () => Promise<T>,
    fallbackFn: () => Promise<T>,
    operation: string = 'AI Analysis'
  ): Promise<T> {
    if (this.state === 'OPEN') {
      const timeSinceLastFail = Date.now() - this.lastFailTime;
      if (timeSinceLastFail < this.resetTimeout) {
        console.warn(`Circuit breaker OPEN for ${operation}, using fallback`);
        return fallbackFn();
      } else {
        this.state = 'HALF_OPEN';
        console.log(`Circuit breaker HALF_OPEN for ${operation}, testing primary`);
      }
    }

    try {
      const result = await primaryFn();
      this.onSuccess(operation);
      return result;
    } catch (error) {
      const wasHalfOpen = this.state === 'HALF_OPEN';
      this.onFailure(operation, error);
      if (wasHalfOpen || this.state === 'OPEN') {
        console.warn(`Circuit breaker using fallback for ${operation}`);
        return fallbackFn();
      }
      throw error;
    }
  }

  private onSuccess(operation: string) {
    if (this.failures > 0) {
      console.log(`Circuit breaker recovered for ${operation}`);
    }
    this.failures = 0;
    this.state = 'CLOSED';
  }

  private onFailure(operation: string, error: unknown) {
    this.failures++;
    this.lastFailTime = Date.now();
    console.warn(
      `Circuit breaker failure ${this.failures}/${this.failureThreshold} for ${operation}:`,
      error instanceof Error ? error.message : String(error)
    );
    if (this.failures >= this.failureThreshold) {
      this.state = 'OPEN';
      console.error(`Circuit breaker OPEN for ${operation} after ${this.failures} failures`);
    }
  }

  getStatus() {
    return {
      state: this.state,
      failures: this.failures,
      lastFailTime: this.lastFailTime,
      timeSinceLastFail: Date.now() - this.lastFailTime,
    };
  }
}

export class ProductService {
  private aiCircuitBreaker = new ProductServiceCircuitBreaker();
  private CACHE_EXPIRATION = APP_CONFIG.CACHE_DURATION_HOURS * 60 * 60 * 1000;

  getCircuitBreakerStatus() {
    return this.aiCircuitBreaker.getStatus();
  }

  private async analyzeWithEnhancedRecovery(
    product: Product,
    userStack?: UserStack[]
  ): Promise<ProductAnalysis> {
    const primaryAnalysis = async (): Promise<ProductAnalysis> => {
      console.log('Attempting AI analysis...');
      const partialAnalysis = await huggingfaceService.generateProductAnalysis(product, true);
      return this.addStackInteractionAnalysis(partialAnalysis, product, userStack);
    };

    const fallbackAnalysis = async (): Promise<ProductAnalysis> => {
      console.log('Using rule-based analysis fallback...');
      try {
        const ruleBasedAnalysis = await this.createRuleBasedAnalysis(product);
        return this.addStackInteractionAnalysis(ruleBasedAnalysis, product, userStack);
      } catch (ruleError) {
        console.warn('Rule-based analysis failed, using basic analysis:', ruleError);
        const basicAnalysis = this.createBasicAnalysis(product);
        return this.addStackInteractionAnalysis(basicAnalysis, product, userStack);
      }
    };

    try {
      return await this.aiCircuitBreaker.execute(primaryAnalysis, fallbackAnalysis, 'Product Analysis');
    } catch (error) {
      console.error('All analysis methods failed, using emergency fallback:', error);
      const emergencyAnalysis = this.createBasicAnalysis(product);
      return this.addStackInteractionAnalysis(emergencyAnalysis, product, userStack);
    }
  }

  private async createRuleBasedAnalysis(product: Product): Promise<Partial<ProductAnalysis>> {
    return {
      overallScore: this.calculateBasicScore(product),
      categoryScores: {
        ingredients: 75,
        bioavailability: 70,
        dosage: 75,
        purity: 80,
        value: 70,
      },
      strengths: [
        {
          point: 'Product information available',
          detail: 'Basic product data successfully retrieved',
          importance: 'medium' as const,
          category: 'quality' as const,
        },
      ],
      weaknesses: [],
      recommendations: {
        goodFor: ['General use'],
        avoidIf: [],
      },
      aiReasoning: 'Analysis completed using rule-based fallback system',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
  }

  private createBasicAnalysis(product: Product): Partial<ProductAnalysis> {
    return {
      overallScore: 65,
      categoryScores: {
        ingredients: 65,
        bioavailability: 65,
        dosage: 65,
        purity: 65,
        value: 65,
      },
      strengths: [
        {
          point: 'Product identified',
          detail: `Successfully identified ${product.name}`,
          importance: 'low' as const,
          category: 'quality' as const,
        },
      ],
      weaknesses: [
        {
          point: 'Limited analysis available',
          detail: 'Detailed analysis temporarily unavailable',
          importance: 'low' as const,
          category: 'quality' as const,
        },
      ],
      recommendations: {
        goodFor: ['Consult healthcare provider for guidance'],
        avoidIf: ['If you have known allergies to ingredients'],
      },
      aiReasoning: 'Basic analysis - detailed AI analysis temporarily unavailable',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
  }

  private calculateBasicScore(product: Product): number {
    let score = 50;
    if (product.ingredients && product.ingredients.length > 0) score += 15;
    if (product.servingSize && product.servingSize !== 'Unknown') score += 10;
    if (product.brand) score += 5;
    if (product.category) score += 5;
    return Math.min(score, 80);
  }

  async analyzeScannedProduct(
    barcode: string,
    userStack?: UserStack[],
    userId?: string
  ): Promise<{ product: Product; analysis: ProductAnalysis }> {
    try {
      const isAllowed = await scanRateLimiter.isAllowed(userId);
      if (!isAllowed) {
        const timeUntilReset = scanRateLimiter.getTimeUntilReset(userId);
        throw new Error(`Scan rate limit exceeded. Try again in ${Math.ceil(timeUntilReset / 1000)} seconds.`);
      }

      console.log('Starting product analysis for:', barcode);

      const cacheKey = `product_analysis_${barcode}`;
      const cached = await aiCache.get(cacheKey);
      if (cached) {
        const cachedResult = JSON.parse(cached);
        if (Date.now() - cachedResult.timestamp < this.CACHE_EXPIRATION) {
          console.log('Using cached product analysis for:', barcode);
          if (userStack && userStack.length > 0) {
            const stackAnalysis = await interactionService.analyzeProductWithStack(cachedResult.product, userStack);
            cachedResult.analysis.stackInteraction = stackAnalysis;
          }
          return cachedResult;
        }
      }

      const product = await multiSourceDataService.fetchProductByBarcode(barcode, userId);
      if (!product) {
        throw new Error('Product not found in database');
      }

      const analysis = await this.analyzeWithEnhancedRecovery(product, userStack);

      const result = { product, analysis, timestamp: Date.now() };
      await aiCache.set(cacheKey, JSON.stringify(result));

      console.log('Product analysis complete:', {
        found: true,
        hasInteractions: analysis.stackInteraction?.overallRiskLevel !== 'NONE',
        name: product.name,
        score: analysis.overallScore,
      });

      return { product, analysis };
    } catch (error) {
      console.error('Product analysis error:', error);
      const fallbackProduct = this.createNotFoundProduct(barcode);
      const fallbackAnalysis = this.createFallbackAnalysis();
      return { product: fallbackProduct, analysis: fallbackAnalysis };
    }
  }

  async analyzeSearchResult(
    searchResult: SearchResult,
    userStack: UserStack[]
  ): Promise<{ product: Product; analysis: ProductAnalysis }> {
    const product = convertSearchResultToProduct(searchResult);
    const analysis = await this.analyzeWithEnhancedRecovery(product, userStack);
    return { product, analysis };
  }

  private async addStackInteractionAnalysis(
    partialAnalysis: Partial<ProductAnalysis>,
    product: Product,
    userStack?: UserStack[]
  ): Promise<ProductAnalysis> {
    const fullAnalysis: ProductAnalysis = {
      overallScore: partialAnalysis.overallScore || 0,
      categoryScores: partialAnalysis.categoryScores || {
        ingredients: 0,
        bioavailability: 0,
        dosage: 0,
        purity: 0,
        value: 0,
      },
      strengths: partialAnalysis.strengths || [],
      weaknesses: partialAnalysis.weaknesses || [],
      recommendations: partialAnalysis.recommendations || { goodFor: [], avoidIf: [] },
      aiReasoning: partialAnalysis.aiReasoning || 'Analysis in progress...',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      generatedAt: partialAnalysis.generatedAt,
    };

    if (userStack && userStack.length > 0) {
      console.log('Checking interactions with user stack...');
      const stackAnalysis = await interactionService.analyzeProductWithStack(product, userStack);
      fullAnalysis.stackInteraction = stackAnalysis;
      console.log(`Stack analysis complete. Risk: ${stackAnalysis.overallRiskLevel}`);
    }

    return fullAnalysis;
  }

  private createFallbackAnalysis(): ProductAnalysis {
    return {
      overallScore: 0,
      categoryScores: { ingredients: 0, bioavailability: 0, dosage: 0, purity: 0, value: 0 },
      strengths: [],
      weaknesses: [
        {
          point: 'Product not found',
          detail: 'Unable to analyze this product. It may not be in our database.',
          importance: 'high',
          category: 'quality',
        },
      ],
      recommendations: { goodFor: [], avoidIf: [] },
      aiReasoning: 'Product not found in database. Please try scanning another product.',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
  }

  private createNotFoundProduct(barcode: string): Product {
    return {
      id: `not_found_${barcode}`,
      name: 'Product Not Found',
      brand: 'Unknown',
      category: 'specialty',
      barcode: barcode,
      ingredients: [],
      servingSize: 'Unknown',
      servingsPerContainer: 1,
      imageUrl: undefined,
      verified: false,
      thirdPartyTested: false,
      certifications: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
  }
}

export const productService = new ProductService();
export async function analyzeScannedProduct(
  barcode: string,
  userStack?: UserStack[],
  userId?: string
): Promise<{ product: Product; analysis: ProductAnalysis }> {
  return productService.analyzeScannedProduct(barcode, userStack, userId);
}