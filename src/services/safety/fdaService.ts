// src/services/safety/fdaService.ts
import axios from 'axios';
import { cacheManager } from '../dataIntegration/cacheManager';
import { rxNormService } from '../normalization/rxNormService';
import { CRITICAL_INTERACTIONS, SUPPLEMENT_INTERACTIONS, APP_CONFIG, API_ENDPOINTS } from '../../constants/index';
import { Interaction, InteractionSeverity, InteractionType, DatabaseSourceReference } from '../../types';

export interface AdverseEvent {
  event: string;
  severity: InteractionSeverity;
  date: string;
  count: number;
  source: string;
}

export interface DrugLabel {
  id: string;
  name: string;
  warnings: string[];
  contraindications: string[];
  interactions: string[];
  lastUpdated: string;
}

export class FDAService {
  private client = axios.create({
    baseURL: API_ENDPOINTS.OPENFDA,
    params: { api_key: APP_CONFIG.FDA_API_KEY },
  });

  async getAdverseEvents(ingredientName: string): Promise<AdverseEvent[]> {
    const cacheKey = `openfda_adverse_${ingredientName.toLowerCase()}`;
    return cacheManager.getCachedOrFetch(cacheKey, async () => {
      try {
        const normalized = await rxNormService.normalizeIngredientName(ingredientName);
        const searchTerm = normalized?.name || ingredientName;
        const response = await this.client.get('/drug/event.json', {
          params: {
            search: `patient.drug.medicinalproduct.exact:"${searchTerm}"`,
            limit: 10,
            sort: 'receivedate:desc',
          },
          timeout: APP_CONFIG.API_TIMEOUT_MS,
        });
        const results = response.data.results || [];
        return results.map((event: any) => ({
          event: event.patient.reaction[0]?.reactionmeddrapt || 'Unknown reaction',
          severity: this.mapSeverity(event.seriousness),
          date: event.receivedate || new Date().toISOString(),
          count: event.occurcountry ? 1 : 0,
          source: 'OpenFDA Adverse Event Reporting System',
        }));
      } catch (error) {
        console.error('OpenFDA adverse event error:', error);
        return [];
      }
    }, { ttl: 7 * 24 * 60 * 60 * 1000 });
  }

  async getDrugLabel(ingredientName: string): Promise<DrugLabel | null> {
    const cacheKey = `openfda_label_${ingredientName.toLowerCase()}`;
    return cacheManager.getCachedOrFetch(cacheKey, async () => {
      try {
        const normalized = await rxNormService.normalizeIngredientName(ingredientName);
        const searchTerm = normalized?.name || ingredientName;
        const response = await this.client.get('/drug/label.json', {
          params: {
            search: `openfda.substance_name.exact:"${searchTerm}"`,
            limit: 1,
          },
          timeout: APP_CONFIG.API_TIMEOUT_MS,
        });
        const result = response.data.results[0];
        if (!result) return null;
        return {
          id: result.id || 'unknown',
          name: result.openfda?.brand_name?.[0] || searchTerm,
          warnings: result.warnings || [],
          contraindications: result.contraindications || [],
          interactions: result.drug_interactions || [],
          lastUpdated: result.effective_time || new Date().toISOString(),
        };
      } catch (error) {
        console.error('OpenFDA drug label error:', error);
        return null;
      }
    }, { ttl: 30 * 24 * 60 * 60 * 1000 });
  }

  async getDrugInteractions(ingredientName: string, stack: any[]): Promise<Interaction[]> {
    const interactions: Interaction[] = [];
    const normalized = await rxNormService.normalizeIngredientName(ingredientName);
    const searchTerm = normalized?.name || ingredientName;

    // Check predefined rules from index.ts
    const criticalRule = CRITICAL_INTERACTIONS[searchTerm.toLowerCase()];
    if (criticalRule) {
      const stackItems = stack.flatMap(item => item.ingredients?.map(ing => ing.name) || [item.name]);
      for (const stackItem of stackItems) {
        if (criticalRule.supplements.includes(stackItem) || criticalRule.medications.includes(stackItem)) {
          interactions.push({
            id: `critical_${searchTerm}_${stackItem}`,
            type: criticalRule.supplements.includes(stackItem) ? 'supplement-medication' : 'medication-medication',
            severity: criticalRule.severity,
            substance1: { name: searchTerm, category: 'medication', commonNames: [searchTerm] },
            substance2: { name: stackItem, category: criticalRule.supplements.includes(stackItem) ? 'supplement' : 'medication', commonNames: [stackItem] },
            mechanism: criticalRule.mechanism,
            description: criticalRule.evidence,
            clinicalSignificance: criticalRule.evidence,
            evidence: {
              level: 'A',
              sources: criticalRule.sources.map(s => s.url),
              studyCount: criticalRule.sources.length,
              lastUpdated: new Date().toISOString(),
            },
            recommendations: {
              action: criticalRule.management.includes('consult') ? 'consult_provider' : 'monitor',
              monitoring: [criticalRule.management],
            },
          });
        }
      }
    }

    const supplementRule = SUPPLEMENT_INTERACTIONS[searchTerm.toLowerCase()];
    if (supplementRule) {
      const stackItems = stack.flatMap(item => item.ingredients?.map(ing => ing.name) || [item.name]);
      for (const stackItem of stackItems) {
        if (supplementRule.conflicting_supplements?.includes(stackItem) || supplementRule.synergistic_supplements?.includes(stackItem)) {
          interactions.push({
            id: `supplement_${searchTerm}_${stackItem}`,
            type: 'supplement-supplement',
            severity: supplementRule.severity,
            substance1: { name: searchTerm, category: 'supplement', commonNames: [searchTerm] },
            substance2: { name: stackItem, category: 'supplement', commonNames: [stackItem] },
            mechanism: supplementRule.mechanism,
            description: supplementRule.evidence,
            clinicalSignificance: supplementRule.evidence,
            evidence: {
              level: 'A',
              sources: supplementRule.sources.map(s => s.url),
              studyCount: supplementRule.sources.length,
              lastUpdated: new Date().toISOString(),
            },
            recommendations: {
              action: supplementRule.management.includes('consult') ? 'consult_provider' : 'monitor',
              monitoring: [supplementRule.management],
            },
          });
        }
      }
    }

    // Fetch from OpenFDA
    try {
      const label = await this.getDrugLabel(ingredientName);
      if (label?.interactions) {
        interactions.push(...label.interactions.map((i: string) => ({
          id: `openfda_${ingredientName}_${i}`,
          type: 'supplement-medication' as InteractionType,
          severity: 'MODERATE' as InteractionSeverity,
          substance1: { name: ingredientName, category: 'supplement', commonNames: [ingredientName] },
          substance2: { name: i, category: 'medication', commonNames: [i] },
          mechanism: 'unknown' as any,
          description: i,
          clinicalSignificance: 'Potential interaction identified in drug labeling',
          evidence: {
            level: 'B' as any,
            sources: ['OpenFDA Drug Labeling'],
            studyCount: 0,
            lastUpdated: new Date().toISOString(),
          },
          recommendations: {
            action: 'consult_provider',
            monitoring: ['Monitor for adverse effects'],
          },
        })));
      }
    } catch (error) {
      console.error('OpenFDA interaction error:', error);
    }

    return interactions;
  }

  private mapSeverity(seriousness: string): InteractionSeverity {
    if (seriousness?.includes('DEATH') || seriousness?.includes('LIFE_THREATENING')) return 'CRITICAL';
    if (seriousness?.includes('HOSPITALIZATION')) return 'MODERATE';
    return 'LOW';
  }
}

export const fdaService = new FDAService();