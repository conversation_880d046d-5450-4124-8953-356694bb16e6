import type { Product, Ingredient, ProductCategory } from '../../types';



src/services/search/productConverter.ts



interface SearchResult {

  id: string;

  name: string;

  brand: string;

  category: string;

  imageUrl?: string;

  source: 'database' | 'dsld' | 'openfoodfacts';

  barcode?: string;

}



export const convertSearchResultToProduct = (searchResult: SearchResult): Product => {

  const productId = searchResult.id || `search_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  const category = mapCategoryString(searchResult.category);

  const ingredients = generateBasicIngredients(category, searchResult.name);



  return {

    id: productId,

    name: searchResult.name,

    brand: searchResult.brand,

    category,

    barcode: searchResult.barcode,

    ingredients,

    servingSize: "1 capsule",

    servingsPerContainer: 30,

    dosage: "As directed",

    price: undefined,

    imageUrl: searchResult.imageUrl,

    verified: searchResult.source === 'dsld',

    thirdPartyTested: false,

    certifications: [],

    createdAt: new Date().toISOString(),

    updatedAt: new Date().toISOString(),

  };

};



const mapCategoryString = (categoryStr: string): ProductCategory => {

  const category = categoryStr.toLowerCase();

  if (category.includes('vitamin')) return 'vitamin';

  if (category.includes('mineral')) return 'mineral';

  if (category.includes('amino') || category.includes('protein')) return 'amino_acid';

  if (category.includes('herbal') || category.includes('herb')) return 'herbal';

  if (category.includes('protein')) return 'protein';

  if (category.includes('probiotic')) return 'probiotic';

  if (category.includes('omega') || category.includes('fish oil')) return 'omega3';

  if (category.includes('multi')) return 'multivitamin';

  return 'specialty';

};



const generateBasicIngredients = (category: ProductCategory, productName: string): Ingredient[] => {

  const name = productName.toLowerCase();

  switch (category) {

    case 'vitamin':

      return generateVitaminIngredients(name);

    case 'mineral':

      return generateMineralIngredients(name);

    case 'omega3':

      return generateOmega3Ingredients();

    case 'probiotic':

      return generateProbioticIngredients();

    case 'protein':

      return generateProteinIngredients();

    case 'multivitamin':

      return generateMultivitaminIngredients();

    case 'herbal':

      return generateHerbalIngredients(name);

    default:

      return generateGenericIngredients();

  }

};



const generateVitaminIngredients = (name: string): Ingredient[] => {

  const vitamins = ['Vitamin A', 'Vitamin B12', 'Vitamin C', 'Vitamin D', 'Vitamin E'];

  const matchedVitamin = vitamins.find(v => name.includes(v.toLowerCase())) || 'Vitamin C';

  return [{ name: matchedVitamin, amount: '100 mg', unit: 'mg', dailyValuePercentage: 100 }];

};



const generateMineralIngredients = (name: string): Ingredient[] => {

  const minerals = ['Calcium', 'Iron', 'Magnesium', 'Zinc'];

  const matchedMineral = minerals.find(m => name.includes(m.toLowerCase())) || 'Calcium';

  return [{ name: matchedMineral, amount: '500 mg', unit: 'mg', dailyValuePercentage: 50 }];

};



const generateOmega3Ingredients = (): Ingredient[] => {

  return [

    { name: 'EPA', amount: '180 mg', unit: 'mg', dailyValuePercentage: null },

    { name: 'DHA', amount: '120 mg', unit: 'mg', dailyValuePercentage: null },

  ];

};



const generateProbioticIngredients = (): Ingredient[] => {

  return [{ name: 'Lactobacillus', amount: '1 billion', unit: 'CFU', dailyValuePercentage: null }];

};



const generateProteinIngredients = (): Ingredient[] => {

  return [{ name: 'Whey Protein', amount: '20 g', unit: 'g', dailyValuePercentage: 40 }];

};



const generateMultivitaminIngredients = (): Ingredient[] => {

  return [

    { name: 'Vitamin C', amount: '60 mg', unit: 'mg', dailyValuePercentage: 100 },

    { name: 'Vitamin D', amount: '400 IU', unit: 'IU', dailyValuePercentage: 100 },

    { name: 'Calcium', amount: '200 mg', unit: 'mg', dailyValuePercentage: 20 },

  ];

};



const generateHerbalIngredients = (name: string): Ingredient[] => {

  const herbs = ['Ginseng', 'Turmeric', 'Ginkgo Biloba', 'Echinacea'];

  const matchedHerb = herbs.find(h => name.includes(h.toLowerCase())) || 'Ginseng';

  return [{ name: matchedHerb, amount: '100 mg', unit: 'mg', dailyValuePercentage: null }];

};



const generateGenericIngredients = (): Ingredient[] => {

  return [{ name: 'Generic Ingredient', amount: '100 mg', unit: 'mg', dailyValuePercentage: null }];

};