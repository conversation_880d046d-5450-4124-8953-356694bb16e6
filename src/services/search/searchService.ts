// src/services/search/searchService.ts
import { multiSourceDataService } from '../dataIntegration/multiSourceDataService';
import { storageAdapter } from '../storage/storageAdapter';
import { AICache } from '../ai/AICache';
import type { Product } from '../../types';
import { STORAGE_KEYS, APP_CONFIG } from '../../constants/index';

interface SearchResult {
  id: string;
  name: string;
  brand: string;
  category: string;
  imageUrl?: string;
  source: 'database' | 'dsld' | 'usda' | 'openfoodfacts';
  barcode?: string;
}

interface SearchSuggestion {
  text: string;
  type: 'recent' | 'popular' | 'category';
}

interface SearchHistory {
  query: string;
  timestamp: string;
  resultCount: number;
}

export class SearchService {
  private aiCache: AICache;
  private rateLimit = {
    limit: 100,
    windowMs: 60 * 1000, // 1 minute
    count: 0,
    resetTime: Date.now(),
  };

  constructor() {
    this.aiCache = new AICache();
  }

  private checkRateLimit(): boolean {
    const now = Date.now();
    if (now > this.rateLimit.resetTime + this.rateLimit.windowMs) {
      this.rateLimit.count = 0;
      this.rateLimit.resetTime = now;
    }
    if (this.rateLimit.count >= this.rateLimit.limit) {
      console.warn(`Search rate limit exceeded. Waiting until ${new Date(this.rateLimit.resetTime + this.rateLimit.windowMs).toISOString()}`);
      return false;
    }
    this.rateLimit.count++;
    return true;
  }

  async searchProducts(query: string): Promise<SearchResult[]> {
    if (!query.trim()) return [];

    if (!this.checkRateLimit()) {
      throw new Error('Search rate limit exceeded. Please try again later.');
    }

    const cacheKey = `${STORAGE_KEYS.PRODUCT_CACHE}:search:${query.toLowerCase().trim()}`;

    // Check cache
    const cached = await this.aiCache.get(cacheKey);
    if (cached) {
      console.log('📦 Using cached search results for:', query);
      return cached as SearchResult[];
    }

    console.log('🔍 Searching for products:', query);

    try {
      const products = await multiSourceDataService.fetchProductsByName(query);
      const results: SearchResult[] = products.map(product => ({
        id: product.id,
        name: product.name,
        brand: product.brand,
        category: product.category,
        imageUrl: product.imageUrl,
        source: product.source || 'database',
        barcode: product.barcode,
      }));

      // Remove duplicates and limit results
      const uniqueResults = this.removeDuplicates(results);
      const limitedResults = uniqueResults.slice(0, APP_CONFIG.MAX_SEARCH_RESULTS);

      // Cache results
      await this.aiCache.set(cacheKey, limitedResults, APP_CONFIG.SEARCH_CACHE_DURATION_MS / 1000);

      // Save to search history
      await this.saveSearchHistory(query, limitedResults.length);

      return limitedResults;
    } catch (error) {
      console.error('Search failed:', error);
      throw new Error(`Search failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private removeDuplicates(results: SearchResult[]): SearchResult[] {
    const seen = new Set<string>();
    return results.filter(result => {
      const key = `${result.name.toLowerCase()}-${result.brand.toLowerCase()}`;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  async getSearchSuggestions(query: string): Promise<SearchSuggestion[]> {
    if (!query.trim()) {
      return this.getDefaultSuggestions();
    }

    const suggestions: SearchSuggestion[] = [];

    // Add recent searches that match
    const recentSearches = await this.getRecentSearches();
    const matchingRecent = recentSearches
      .filter(search => search.query.toLowerCase().includes(query.toLowerCase()))
      .slice(0, 3)
      .map(search => ({
        text: search.query,
        type: 'recent' as const,
      }));

    suggestions.push(...matchingRecent);

    // Add category suggestions
    const categoryMatches = this.getCategorySuggestions(query);
    suggestions.push(...categoryMatches);

    return suggestions.slice(0, 5);
  }

  private async getDefaultSuggestions(): Promise<SearchSuggestion[]> {
    const suggestions: SearchSuggestion[] = [];

    // Add recent searches
    const recentSearches = await this.getRecentSearches();
    const recentSuggestions = recentSearches.slice(0, 3).map(search => ({
      text: search.query,
      type: 'recent' as const,
    }));

    suggestions.push(...recentSuggestions);

    // Add popular categories
    const popularCategories = [
      'Vitamin D',
      'Omega-3',
      'Multivitamin',
      'Protein Powder',
      'Probiotics',
    ];

    const categorySuggestions = popularCategories.map(category => ({
      text: category,
      type: 'popular' as const,
    }));

    suggestions.push(...categorySuggestions);

    return suggestions.slice(0, 5);
  }

  private getCategorySuggestions(query: string): SearchSuggestion[] {
    const categories = [
      'Vitamin A',
      'Vitamin B',
      'Vitamin C',
      'Vitamin D',
      'Vitamin E',
      'Vitamin K',
      'Calcium',
      'Iron',
      'Magnesium',
      'Zinc',
      'Potassium',
      'Omega-3',
      'Fish Oil',
      'Protein',
      'Creatine',
      'BCAA',
      'Probiotics',
      'Prebiotics',
      'Digestive Enzymes',
      'Multivitamin',
      'Prenatal',
      "Men's Health",
      "Women's Health",
    ];

    return categories
      .filter(category => category.toLowerCase().includes(query.toLowerCase()))
      .slice(0, 3)
      .map(category => ({
        text: category,
        type: 'category' as const,
      }));
  }

  async saveSearchHistory(query: string, resultCount: number): Promise<void> {
    try {
      const history = await this.getRecentSearches();
      const filteredHistory = history.filter(item => item.query !== query);
      const newHistory: SearchHistory[] = [
        {
          query,
          timestamp: new Date().toISOString(),
          resultCount,
        },
        ...filteredHistory,
      ].slice(0, APP_CONFIG.MAX_RECENT_SEARCHES);

      await storageAdapter.setItem(STORAGE_KEYS.RECENT_SEARCHES, JSON.stringify(newHistory));
    } catch (error) {
      console.error('Failed to save search history:', error);
      throw error;
    }
  }

  async getRecentSearches(): Promise<SearchHistory[]> {
    try {
      const stored = await storageAdapter.getItem(STORAGE_KEYS.RECENT_SEARCHES);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Failed to get recent searches:', error);
      return [];
    }
  }

  async clearSearchHistory(): Promise<void> {
    try {
      await storageAdapter.removeItem(STORAGE_KEYS.RECENT_SEARCHES);
    } catch (error) {
      console.error('Failed to clear search history:', error);
      throw error;
    }
  }

  async clearCache(): Promise<void> {
    try {
      // Clear only search-related cache entries
      const keys = await this.aiCache.getAllKeys();
      for (const key of keys) {
        if (key.startsWith(`${STORAGE_KEYS.PRODUCT_CACHE}:search:`)) {
          await this.aiCache.delete(key);
        }
      }
      this.rateLimit.count = 0;
      this.rateLimit.resetTime = Date.now();
    } catch (error) {
      console.error('Failed to clear search cache:', error);
    }
  }
}

export const searchService = new SearchService();