// src/services/stack/stackStore.ts

import create from 'zustand';
import AsyncStorage from '@react-native-async-storage/async-storage';
import debounce from 'lodash/debounce';
import { supabase } from '../supabase/client';
import { UserStack, Product } from '../../types';
import { APP_CONFIG, STORAGE_KEYS, ERROR_MESSAGES, GAMIFICATION } from '../../constants/index';
import { gamificationService } from '../gamification/gamificationService';

interface StackState {
  stack: UserStack[];
  isLoading: boolean;
  error: string | null;
  addToStack: (item: UserStack, userId: string, isProUser?: boolean) => Promise<void>;
  removeFromStack: (itemId: string, userId: string) => Promise<void>;
  updateStackItem: (itemId: string, updates: Partial<UserStack>, userId: string) => Promise<void>;
  syncWithDatabase: (userId: string, changedItemId?: string) => Promise<void>;
  clearStack: (userId: string) => Promise<void>;
  getStackSize: () => number;
}

export const useStackStore = create<StackState>((set, get) => {
  // Debounced sync to prevent excessive Supabase calls
  const debouncedSync = debounce(async (userId: string, changedItemId?: string) => {
    await get().syncWithDatabase(userId, changedItemId);
  }, APP_CONFIG.DEBOUNCE_TIME_MS);

  return {
    stack: [],
    isLoading: false,
    error: null,

    addToStack: async (item: UserStack, userId: string, isProUser = false) => {
      try {
        set({ isLoading: true, error: null });

        // Validate input
        if (!userId) throw new Error(ERROR_MESSAGES.AUTH_ERROR);
        if (!item.item_id || get().stack.some(s => s.item_id === item.item_id)) {
          throw new Error('Duplicate or invalid item ID');
        }
        if (item.ingredients && !Array.isArray(item.ingredients)) {
          throw new Error('Ingredients must be an array');
        }

        // Check stack size limit
        const maxItems = isProUser ? APP_CONFIG.MAX_STACK_ITEMS_PRO : APP_CONFIG.MAX_STACK_ITEMS_FREE;
        if (get().stack.length >= maxItems) {
          throw new Error(ERROR_MESSAGES.STACK_FULL);
        }

        // Optimistic update
        const previousStack = get().stack;
        const newStack = [...previousStack, { ...item, user_id: userId, created_at: new Date().toISOString(), updated_at: new Date().toISOString() }];
        set({ stack: newStack });

        try {
          // Persist to AsyncStorage
          await AsyncStorage.setItem(
            `${STORAGE_KEYS.STACK_PREFIX}${userId}`,
            JSON.stringify(newStack)
          );

          // Sync with Supabase if allowed
          if (!APP_CONFIG.HIPAA_LOCAL_ONLY) {
            const { error } = await supabase.from('user_stack').insert({
              ...item,
              user_id: userId,
              ingredients: item.ingredients || [],
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            });
            if (error) throw error;
          }

          // Award gamification points
          await gamificationService.awardPoints('STACK_ADD');
          if (newStack.length >= APP_CONFIG.ACHIEVEMENT_THRESHOLDS.STACK_BUILDER) {
            await gamificationService.awardPoints('STACK_BUILDER');
          }

          // Trigger debounced sync
          debouncedSync(userId, item.item_id);
        } catch (error) {
          // Rollback on failure
          set({ stack: previousStack, error: error.message || ERROR_MESSAGES.STACK_ERROR });
          throw error;
        } finally {
          set({ isLoading: false });
        }
      } catch (error) {
        set({ isLoading: false, error: error.message || ERROR_MESSAGES.STACK_ERROR });
        console.error('Error adding to stack:', error);
        throw error;
      }
    },

    removeFromStack: async (itemId: string, userId: string) => {
      try {
        set({ isLoading: true, error: null });

        // Validate input
        if (!userId) throw new Error(ERROR_MESSAGES.AUTH_ERROR);
        if (!get().stack.some(s => s.item_id === itemId)) {
          throw new Error('Item not found in stack');
        }

        // Optimistic update
        const previousStack = get().stack;
        const newStack = previousStack.filter(item => item.item_id !== itemId);
        set({ stack: newStack });

        try {
          // Persist to AsyncStorage
          await AsyncStorage.setItem(
            `${STORAGE_KEYS.STACK_PREFIX}${userId}`,
            JSON.stringify(newStack)
          );

          // Delete from Supabase if allowed
          if (!APP_CONFIG.HIPAA_LOCAL_ONLY) {
            const { error } = await supabase.from('user_stack').delete().eq('item_id', itemId).eq('user_id', userId);
            if (error) throw error;
          }

          // Award gamification points
          await gamificationService.awardPoints('STACK_REMOVE');

          // Trigger debounced sync
          debouncedSync(userId, itemId);
        } catch (error) {
          // Rollback on failure
          set({ stack: previousStack, error: error.message || ERROR_MESSAGES.STACK_ERROR });
          throw error;
        } finally {
          set({ isLoading: false });
        }
      } catch (error) {
        set({ isLoading: false, error: error.message || ERROR_MESSAGES.STACK_ERROR });
        console.error('Error removing from stack:', error);
        throw error;
      }
    },

    updateStackItem: async (itemId: string, updates: Partial<UserStack>, userId: string) => {
      try {
        set({ isLoading: true, error: null });

        // Validate input
        if (!userId) throw new Error(ERROR_MESSAGES.AUTH_ERROR);
        if (!get().stack.some(s => s.item_id === itemId)) {
          throw new Error('Item not found in stack');
        }
        if (updates.ingredients && !Array.isArray(updates.ingredients)) {
          throw new Error('Ingredients must be an array');
        }

        // Optimistic update
        const previousStack = get().stack;
        const newStack = previousStack.map(item =>
          item.item_id === itemId ? { ...item, ...updates, updated_at: new Date().toISOString() } : item
        );
        set({ stack: newStack });

        try {
          // Persist to AsyncStorage
          await AsyncStorage.setItem(
            `${STORAGE_KEYS.STACK_PREFIX}${userId}`,
            JSON.stringify(newStack)
          );

          // Update Supabase if allowed
          if (!APP_CONFIG.HIPAA_LOCAL_ONLY) {
            const { error } = await supabase.from('user_stack').update({
              ...updates,
              ingredients: updates.ingredients || [],
              updated_at: new Date().toISOString(),
            }).eq('item_id', itemId).eq('user_id', userId);
            if (error) throw error;
          }

          // Award gamification points
          await gamificationService.awardPoints('STACK_UPDATE');

          // Trigger debounced sync
          debouncedSync(userId, itemId);
        } catch (error) {
          // Rollback on failure
          set({ stack: previousStack, error: error.message || ERROR_MESSAGES.STACK_ERROR });
          throw error;
        } finally {
          set({ isLoading: false });
        }
      } catch (error) {
        set({ isLoading: false, error: error.message || ERROR_MESSAGES.STACK_ERROR });
        console.error('Error updating stack item:', error);
        throw error;
      }
    },

    syncWithDatabase: async (userId: string, changedItemId?: string) => {
      try {
        set({ isLoading: true, error: null });

        if (APP_CONFIG.HIPAA_LOCAL_ONLY) {
          return;
        }

        const localStack = get().stack.filter(s => s.user_id === userId);
        let query = supabase.from('user_stack').select('*').eq('user_id', userId);
        if (changedItemId) {
          query = query.eq('item_id', changedItemId);
        }

        const { data: dbStack, error } = await query;
        if (error) {
          throw new Error(`Error fetching user stack: ${error.message}`);
        }

        // Determine items to insert or update
        const toInsert = localStack.filter(
          local => !dbStack?.some(db => db.item_id === local.item_id)
        );
        const toUpdate = localStack.filter(local =>
          dbStack?.some(db => db.item_id === local.item_id && new Date(db.updated_at) < new Date(local.updated_at))
        );
        const toDelete = dbStack?.filter(
          db => !localStack.some(local => local.item_id === db.item_id)
        ) || [];

        // Batch operations
        await Promise.all([
          ...toInsert.map(item =>
            supabase.from('user_stack').insert({
              ...item,
              user_id: userId,
              ingredients: item.ingredients || [],
              created_at: item.created_at || new Date().toISOString(),
              updated_at: item.updated_at || new Date().toISOString(),
            })
          ),
          ...toUpdate.map(item =>
            supabase.from('user_stack').update({
              ...item,
              ingredients: item.ingredients || [],
              updated_at: new Date().toISOString(),
            }).eq('item_id', item.item_id).eq('user_id', userId)
          ),
          ...toDelete.map(item =>
            supabase.from('user_stack').delete().eq('item_id', item.item_id).eq('user_id', userId)
          ),
        ]);

        // Update local stack with database changes
        const updatedStack = [
          ...localStack.filter(s => !dbStack?.some(db => db.item_id === s.item_id)),
          ...(dbStack?.map(db => ({
            ...db,
            ingredients: db.ingredients || [],
            created_at: db.created_at || new Date().toISOString(),
            updated_at: db.updated_at || new Date().toISOString(),
          })) || []),
        ];
        set({ stack: updatedStack });

        await AsyncStorage.setItem(
          `${STORAGE_KEYS.STACK_PREFIX}${userId}`,
          JSON.stringify(updatedStack)
        );
      } catch (error) {
        set({ error: error.message || ERROR_MESSAGES.STACK_ERROR });
        console.error('Error syncing stack with database:', error);
      } finally {
        set({ isLoading: false });
      }
    },

    clearStack: async (userId: string) => {
      try {
        set({ isLoading: true, error: null });

        if (!userId) throw new Error(ERROR_MESSAGES.AUTH_ERROR);

        set({ stack: [] });
        await AsyncStorage.removeItem(`${STORAGE_KEYS.STACK_PREFIX}${userId}`);

        if (!APP_CONFIG.HIPAA_LOCAL_ONLY) {
          const { error } = await supabase.from('user_stack').delete().eq('user_id', userId);
          if (error) throw error;
        }
      } catch (error) {
        set({ error: error.message || ERROR_MESSAGES.STACK_ERROR });
        console.error('Error clearing stack:', error);
        throw error;
      } finally {
        set({ isLoading: false });
      }
    },

    getStackSize: () => get().stack.length,
  };
});

// Export a utility to convert Product to UserStack
export const convertProductToUserStack = (product: Product, userId: string): UserStack => ({
  item_id: product.id,
  user_id: userId,
  product_id: product.id,
  name: product.name,
  brand: product.brand,
  category: product.category,
  ingredients: product.ingredients || [],
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
});