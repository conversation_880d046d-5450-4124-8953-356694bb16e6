// src/types/database.ts

// ===== Base Database Types =====

export interface DatabaseUser {
  id: string;
  auth_id: string;
  email: string | null;
  is_anonymous: boolean;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

export interface DatabaseProduct {
  id: string;
  barcode: string | null;
  name: string;
  generic_name: string | null;
  brand: string | null;
  manufacturer: string | null;
  category: string | null; // Matches product_category enum
  dosage_form: string | null;
  strength: string | null;
  active_ingredients: any | null; // JSONB
  inactive_ingredients: string[] | null;
  image_url: string | null;
  verified: boolean;
  fda_approved: boolean;
  otc_status: boolean;
  warnings: string[] | null;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  search_vector?: any; // tsvector for full-text search
}

export interface DatabaseUserProfile {
  id: string;
  user_id: string;
  display_name: string | null;
  avatar_url: string | null;
  bio: string | null;
  date_of_birth: string | null; // date
  gender: string | null; // Matches Supabase schema
  height_cm: number | null;
  weight_kg: number | null;
  activity_level: string | null;
  health_goals: string[] | null; // text[]
  medical_conditions: string[] | null; // text[]
  allergies: string[] | null; // text[]
  medications: string[] | null; // text[]
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

export interface DatabaseUserPreferences {
  id: string;
  user_id: string;
  theme: string;
  notifications_enabled: boolean;
  email_notifications: boolean;
  push_notifications: boolean;
  language: string;
  timezone: string;
  privacy_level: string;
  data_sharing_consent: boolean;
  created_at: string;
  updated_at: string;
}

export interface DatabaseUserPoints {
  id: string;
  user_id: string;
  total_points: number;
  level: number;
  created_at: string;
  updated_at: string;
}

export interface DatabaseUserStreaks {
  id: string;
  user_id: string;
  current_streak: number;
  longest_streak: number;
  last_scan_date: string | null; // Date string
  created_at: string;
  updated_at: string;
}

export interface DatabasePointsHistory {
  id: string;
  user_id: string;
  points_change: number;
  reason: string;
  metadata: Record<string, any> | null;
  created_at: string;
}

export interface DatabaseScanHistory {
  id: string;
  user_id: string | null; // Can be null for anonymous scans
  product_id: string | null;
  scan_type: string | null;
  analysis_score: number | null;
  scanned_at: string;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

export interface DatabaseUserStack {
  id: string;
  user_id: string;
  item_id: string | null;
  name: string | null;
  type: 'medication' | 'supplement' | null;
  dosage: string | null;
  frequency: string | null;
  active: boolean;
  brand: string | null;
  image_url: string | null;
  ingredients: any | null; // JSONB
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

export interface DatabaseInteraction {
  id: string;
  item1_id: string;
  item2_id: string;
  severity: 'LOW' | 'MODERATE' | 'HIGH' | 'CRITICAL' | null;
  mechanism: string | null;
  evidence: string | null;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

export interface CriticalInteractionRule {
  id: string;
  item1Type: string;
  item1Identifier: string;
  item2Type: string;
  item2Identifier: string;
  severity: 'LOW' | 'MODERATE' | 'HIGH' | 'CRITICAL';
  mechanism: string;
  clinicalSignificance: string;
  recommendation: string;
  contraindicated: boolean;
  monitoringRequired: boolean;
  source: 'FDA' | 'NIH' | 'PUBMED' | 'CLINICAL_STUDY' | 'MANUFACTURER' | 'AI';
  evidenceQuality: 'A' | 'B' | 'C' | 'D' | 'EXPERT_OPINION';
  sources: InteractionSource[];
}

export interface InteractionSource {
  sourceType:
    | 'FDA'
    | 'NIH'
    | 'PUBMED'
    | 'CLINICAL_STUDY'
    | 'MANUFACTURER'
    | 'AI';
  sourceName: string;
  sourceUrl?: string;
  confidenceScore?: number;
}

export interface NutrientLimit {
  id: string;
  nutrientName: string;
  upperLimit: number;
  unit: string;
  ageGroup?: string;
  gender?: string;
  healthRisks?: string;
  toxicitySymptoms?: string[];
  source: string;
  sourceUrl?: string;
}

export interface DatabaseAIResponseCache {
  id: string;
  cache_key: string;
  query_hash: string;
  response: any; // JSONB
  model_used: string | null;
  provider: string | null;
  response_time_ms: number | null;
  token_count: number | null;
  cost: number | null;
  quality_score: number | null;
  hit_count: number;
  user_id: string | null;
  created_at: string;
  updated_at: string;
  expires_at: string | null;
  last_accessed: string;
}

export interface DatabaseUserRole {
  id: string;
  user_id: string;
  role: 'patient' | 'provider' | 'admin' | 'moderator';
  granted_at: string;
  granted_by: string | null;
  expires_at: string | null;
  metadata: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface DatabaseProviderPatient {
  id: string;
  provider_id: string;
  patient_id: string;
  status: 'pending' | 'active' | 'revoked';
  shared_data: {
    stack: boolean;
    history: boolean;
    profile: boolean;
  };
  invitation_code: string | null;
  invited_at: string;
  accepted_at: string | null;
  revoked_at: string | null;
  created_at: string;
  updated_at: string;
}

// ===== New Database Types =====

export interface DatabaseCriticalInteractionRule {
  id: string;
  item1_type: string;
  item1_identifier: string;
  item2_type: string;
  item2_identifier: string;
  severity: 'LOW' | 'MODERATE' | 'HIGH' | 'CRITICAL';
  mechanism: string;
  clinical_significance: string;
  recommendation: string;
  contraindicated: boolean;
  monitoring_required: boolean;
  source: 'FDA' | 'NIH' | 'PUBMED' | 'CLINICAL_STUDY' | 'MANUFACTURER' | 'AI';
  source_url: string | null;
  evidence_quality: 'A' | 'B' | 'C' | 'D' | 'EXPERT_OPINION' | null;
  version: number;
  last_reviewed: string | null;
  created_at: string;
  verified_by: string | null;
  verified_at: string | null;
  active: boolean;
  superseded_by: string | null;
  version_notes: string | null;
}

export interface DatabaseInteractionSource {
  id: string;
  interaction_id: string | null;
  source_type: 'FDA' | 'NIH' | 'PUBMED' | 'CLINICAL_STUDY' | 'MANUFACTURER' | 'AI';
  source_name: string;
  source_url: string | null;
  publication_date: string | null;
  study_type: string | null;
  confidence_score: number | null;
  notes: string | null;
  created_at: string;
}

export interface DatabaseFeedbackReport {
  id: string;
  report_type: string;
  related_product_id: string | null;
  related_interaction_id: string | null;
  description: string;
  severity: string | null;
  status: string;
  resolution: string | null;
  created_at: string;
  resolved_at: string | null;
  resulted_in_rule_id: string | null;
  implemented_at: string | null;
  implementation_notes: string | null;
}

export interface DatabaseAIModelMetrics {
  id: string;
  model_name: string;
  provider: string;
  request_count: number;
  success_count: number;
  error_count: number;
  total_tokens: number;
  total_cost: number;
  avg_response_time_ms: number | null;
  avg_quality_score: number | null;
  error_rate: number | null;
  last_used: string | null;
  created_at: string;
  updated_at: string;
}

export interface DatabaseUserSubmission {
  id: string;
  submission_id: string;
  product_name: string;
  barcode: string | null;
  brand: string | null;
  front_image_url: string | null;
  back_image_url: string | null;
  ingredients_image_url: string | null;
  status: string;
  rejection_reason: string | null;
  approved_product_id: string | null;
  submitted_at: string;
  reviewed_at: string | null;
  reviewer_notes: string | null;
}

// ===== Joined Query Types =====

export interface DatabaseUserWithRelations extends DatabaseUser {
  profile?: DatabaseUserProfile;
  preferences?: DatabaseUserPreferences;
  points?: DatabaseUserPoints;
  streaks?: DatabaseUserStreaks;
  roles?: DatabaseUserRole[];
}

// ===== Function Return Types =====

export interface CreateUserWithProfileResponse {
  user_id: string;
}

export interface IncrementPointsResponse {
  success: boolean;
  new_total: number;
  new_level: number;
}

export interface UpdateStreakResponse {
  success: boolean;
  current_streak: number;
  longest_streak: number;
  message?: string;
}

// ===== Application Types (Used in your React Native app) =====

export interface User {
  id: string;
  authId: string;
  email: string | null;
  isAnonymous: boolean;
  displayName: string | null;
  profile?: UserProfile;
  preferences?: UserPreferences;
  points?: UserPoints;
  streaks?: UserStreaks;
  roles?: UserRole[];
  createdAt: string;
  updatedAt: string;
}

export interface UserProfile {
  firstName: string | null;
  lastName: string | null;
  age: number | null;
  gender: string | null;
  healthGoals: string[];
  conditions: string[];
  allergies: string[];
  medications: string[];
  genetics?: {
    mthfr?: string;
    comt?: string;
    [key: string]: string | undefined;
  };
}

export interface UserPreferences {
  theme: 'system' | 'light' | 'dark';
  notificationsEnabled: boolean;
  emailNotifications: boolean;
  pushNotifications: boolean;
  language: string;
  timezone: string;
  privacyLevel: 'minimal' | 'standard' | 'full';
  dataSharingConsent: boolean;
}

export interface UserPoints {
  total: number;
  level: number;
  levelTitle?: string;
  nextLevelAt?: number;
}

export interface UserStreaks {
  current: number;
  longest: number;
  lastActivity: string | null; // Maps from last_scan_date
}

export interface UserRole {
  role: 'patient' | 'provider' | 'admin' | 'moderator';
  grantedAt: string;
  expiresAt: string | null;
}

export interface Product {
  id: string;
  barcode: string | null;
  name: string;
  brand: string | null;
  category: string | null;
  imageUrl: string | null;
  ingredients: Ingredient[];
  verified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Ingredient {
  name: string;
  amount?: string;
  unit?: string;
  percentage?: number;
}

export interface StackItem {
  id: string;
  itemId: string | null;
  name: string;
  type: 'medication' | 'supplement';
  dosage: string | null;
  frequency: string | null;
  active: boolean;
  brand: string | null;
  imageUrl: string | null;
  ingredients: Ingredient[];
  createdAt: string;
  updatedAt: string;
}

export interface Interaction {
  id: string;
  item1Id: string;
  item2Id: string;
  severity: 'LOW' | 'MODERATE' | 'HIGH' | 'CRITICAL';
  mechanism: string;
  evidence: string;
}

export interface ScanResult {
  id: string;
  productId: string | null;
  product?: Product;
  scanType: 'barcode' | 'ocr' | 'voice' | 'manual';
  analysisScore: number;
  scannedAt: string;
}

// ===== Supabase Table Names =====

export const TABLES = {
  USERS: 'users',
  PRODUCTS: 'products',
  USER_PROFILES: 'user_profiles',
  USER_PREFERENCES: 'user_preferences',
  USER_POINTS: 'user_points',
  USER_STREAKS: 'user_streaks',
  POINTS_HISTORY: 'points_history',
  SCAN_HISTORY: 'scan_history',
  USER_STACK: 'user_stack',
  INTERACTIONS: 'interactions',
  AI_RESPONSE_CACHE: 'ai_response_cache',
  USER_ROLES: 'user_roles',
  PROVIDER_PATIENTS: 'provider_patients',
  AUDIT_LOG: 'audit_log',
  CRITICAL_INTERACTION_RULES: 'critical_interaction_rules',
  INTERACTION_SOURCES: 'interaction_sources',
  FEEDBACK_REPORTS: 'feedback_reports',
  AI_MODEL_METRICS: 'ai_model_metrics',
  USER_SUBMISSIONS: 'user_submissions',
  ANALYTICS_EVENTS: 'analytics_events',
  ANALYTICS_ROLLUP: 'analytics_rollup',
  INTERACTION_CHECK_LOGS: 'interaction_check_logs',
} as const;

// ===== RPC Function Names =====

export const RPC_FUNCTIONS = {
  CREATE_USER_WITH_PROFILE: 'create_user_with_profile',
  INCREMENT_POINTS: 'increment_points',
  UPDATE_STREAK: 'update_streak',
  FIND_ALL_INTERACTIONS: 'find_all_interactions',
  GET_INTERACTION_TIER: 'get_interaction_tier',
  NORMALIZE_INTERACTION_CHECK: 'normalize_interaction_check',
  UPDATE_AI_MODEL_PERFORMANCE: 'update_ai_model_performance',
  SEARCH_PRODUCTS: 'search_products',
  CLEANUP_EXPIRED_AI_CACHE: 'cleanup_expired_ai_cache',
} as const;

// ===== Storage Buckets =====

export const STORAGE_BUCKETS = {
  PRODUCT_IMAGES: 'product-images',
  USER_UPLOADS: 'user-uploads',
} as const;
