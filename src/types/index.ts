// src/types/index.ts
import type { DatabaseProduct, DatabaseUserStack, DatabaseUserProfile } from './database';
import type { UserProfile as HealthProfile } from './healthProfile';

export * from './navigation';
export * from './healthProfile';
export * from './database';

export type StackItem = UserStack;

export interface User {
  id: string;
  email: string | null;
  is_anonymous: boolean;
  profile: HealthProfile;
  preferences: UserPreferences;
  points?: UserPoints;
  streaks?: UserStreaks;
  createdAt: string;
  updatedAt: string;
}

export interface UserPreferences {
  aiResponseStyle: 'concise' | 'detailed' | 'technical';
  budgetRange: 'budget' | 'mid' | 'premium';
  primaryFocus: 'safety' | 'efficacy' | 'value' | 'naturalness';
  notifications: {
    push_enabled: boolean;
    email_enabled: boolean;
    reminder_frequency: string;
  };
}

export interface UserPoints {
  total: number;
  level: number;
  levelTitle: string;
  nextLevelAt: number;
}

export interface UserStreaks {
  current: number;
  longest: number;
  lastActivity: string | null;
}

export interface Product {
  id: string;
  name: string;
  brand: string | null;
  category: ProductCategory;
  barcode?: string;
  ingredients: Ingredient[];
  servingSize: string;
  servingsPerContainer: number;
  dosage?: string;
  price?: number;
  imageUrl?: string;
  verified: boolean;
  thirdPartyTested: boolean;
  certifications: string[];
  createdAt: string;
  updatedAt: string;
}

export interface Ingredient {
  name: string;
  amount: number | null;
  unit: string | null;
  form: IngredientForm;
  dailyValuePercentage?: number;
  bioavailability: 'low' | 'medium' | 'high';
  evidenceLevel: EvidenceLevel;
  category: 'active' | 'inactive' | 'excipient';
}

export interface AnalysisPoint {
  point: string;
  detail: string;
  importance: 'low' | 'medium' | 'high';
  category: 'safety' | 'efficacy' | 'quality' | 'value';
}

export interface SafetyCheck {
  status: 'safe' | 'caution' | 'warning' | 'critical';
  interactions: DrugInteraction[];
  contraindications: string[];
  warnings: string[];
}

export interface DrugInteraction {
  type: 'drug-drug' | 'drug-supplement' | 'supplement-supplement';
  severity: 'minor' | 'moderate' | 'major' | 'critical';
  description: string;
  recommendation: string;
  source: string;
}

export type ProductCategory =
  | 'vitamin'
  | 'mineral'
  | 'amino_acid'
  | 'herbal'
  | 'protein'
  | 'probiotic'
  | 'omega3'
  | 'multivitamin'
  | 'specialty';

export type IngredientForm =
  | 'methylcobalamin'
  | 'cyanocobalamin'
  | 'methylfolate'
  | 'folic_acid'
  | 'chelated'
  | 'citrate'
  | 'oxide'
  | 'glycinate'
  | 'liposomal'
  | 'other';

export type EvidenceLevel =
  | 'meta_analysis'
  | 'rct_studies'
  | 'clinical_trials'
  | 'observational'
  | 'case_reports'
  | 'theoretical'
  | 'marketing_claims';

export type VerificationLevel = 'A' | 'B' | 'C' | 'D' | 'E';

export interface DatabaseSourceReference {
  id: string;
  source: string;
  url?: string;
  accessDate?: string;
  title?: string;
  authors?: string[];
  verificationLevel: VerificationLevel;
  // Extended properties for UI display
  type?: VerificationLevel;
  pmid?: string;
  doi?: string;
  journal?: string;
  year?: number;
  evidenceLevel?: string;
  relevanceScore?: number;
  excerpt?: string;
}

export type RiskLevel = 'NONE' | 'LOW' | 'MODERATE' | 'HIGH' | 'CRITICAL';

export interface InteractionDetail {
  type: 'Drug-Drug' | 'Drug-Supplement' | 'Supplement-Supplement' | 'Nutrient-Limit';
  severity: RiskLevel;
  message: string;
  mechanism?: string;
  evidenceSources?: { badge: string; text: string }[];
  recommendation: string;
}

export interface NutrientWarning {
  nutrient: string;
  currentTotal: number;
  upperLimit: number;
  unit: string;
  risk: string;
  percentOfLimit: number;
  severity: RiskLevel;
  recommendation: string;
}

export interface StackInteractionResult {
  overallRiskLevel: RiskLevel;
  interactions: InteractionDetail[];
  nutrientWarnings?: NutrientWarning[];
  overallSafe: boolean;
}

export interface ProductAnalysis {
  productId?: string;
  overallScore: number;
  categoryScores: CategoryScores;
  strengths: AnalysisPoint[];
  weaknesses: AnalysisPoint[];
  recommendations: {
    goodFor: string[];
    avoidIf: string[];
  };
  alternatives?: Alternative[];
  safetyCheck?: SafetyCheck;
  aiReasoning: string;
  stackInteraction?: StackInteractionResult;
  generatedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Alternative {
  name: string;
  brand: string;
  reasonForSuggestion: string;
  priceRange: string;
  link?: string;
}

export interface CategoryScores {
  ingredients: number;
  bioavailability: number;
  dosage: number;
  purity: number;
  value: number;
}

export interface UserStack {
  id: string;
  user_id: string;
  item_id: string;
  name: string;
  type: 'medication' | 'supplement';
  dosage: string;
  frequency: string;
  brand?: string;
  imageUrl?: string;
  ingredients?: { name: string; amount?: number; unit?: string }[];
  active: boolean;
  created_at: string;
  updated_at: string;
}

// Enhanced Analysis Types from EnhancedIngredientAnalyzer
export interface EnhancedIngredientAnalysis {
  ingredient: string;
  overallScore: number;
  scores: {
    quality: number;
    bioavailability: number;
    dosage: number;
    safety: number;
    evidence: number;
    purity: number;
  };
  formAnalysis: {
    currentForm: string;
    bioavailabilityRating: 'poor' | 'fair' | 'good' | 'excellent';
    alternativeForms: FormAlternative[];
    absorptionFactors: AbsorptionFactor[];
  };
  dosageAnalysis: {
    providedAmount: number;
    unit: string;
    rdi?: number;
    rdiUnit?: string;
    percentRDI?: number;
    therapeuticRange?: { min: number; max: number; unit: string };
    dosageAssessment: 'below' | 'optimal' | 'high' | 'excessive';
    demographicAdjustment?: string;
  };
  safetyAnalysis: {
    upperLimit?: number;
    upperLimitUnit?: string;
    percentOfUpperLimit?: number;
    contraindications: Contraindication[];
    interactions: IngredientInteraction[];
    sideEffects: SideEffect[];
    pregnancyCategory?: string;
    specialPopulations: SpecialPopulationWarning[];
  };
  clinicalEvidence: {
    evidenceLevel: 'A' | 'B' | 'C' | 'D' | 'F';
    studyCount: number;
    metaAnalysisCount: number;
    primaryUses: ClinicalUse[];
    efficacyByCondition: Map<string, number>;
    keyFindings: string[];
  };
  qualityIndicators: {
    sourcingQuality: 'unknown' | 'standard' | 'premium' | 'pharmaceutical';
    manufacturingStandards: string[];
    contaminantRisk: 'low' | 'medium' | 'high';
    shelfStability: 'poor' | 'fair' | 'good' | 'excellent';
  };
  personalizedInsights?: {
    geneticConsiderations?: GeneticFactor[];
    conditionSpecific?: ConditionRecommendation[];
    lifestyleFactors?: LifestyleFactor[];
    stackSynergies?: StackSynergy[];
  };
  recommendations: Recommendation[];
  warnings: Warning[];
  dataSources: string[];
}

export interface FormAlternative {
  name: string;
  bioavailabilityIncrease: string;
  cost: string;
  notes?: string;
}

export interface AbsorptionFactor {
  factor: string;
  effect: 'enhance' | 'inhibit' | 'compete' | 'required';
  magnitude: string;
  timing: string;
}

export interface Contraindication {
  condition: string;
  severity: 'absolute' | 'relative';
  reason: string;
  source: string;
}

export interface IngredientInteraction {
  substance: string;
  severity: 'minor' | 'moderate' | 'major';
  effect: string;
  mechanism: string;
  management: string;
}

export interface SideEffect {
  effect: string;
  frequency: 'rare' | 'common' | 'very common';
  severity: 'mild' | 'moderate' | 'severe';
  notes: string;
}

export interface SpecialPopulationWarning {
  population: string;
  concern: string;
  recommendation: string;
}

export interface ClinicalUse {
  condition: string;
  evidenceCount: number;
  strength: 'strong' | 'moderate' | 'preliminary';
}

export interface GeneticFactor {
  gene: string;
  variant: string;
  impact: string;
  recommendation: string;
  dosageAdjustment?: string;
}

export interface ConditionRecommendation {
  condition: string;
  relevance: 'high' | 'moderate' | 'low';
  evidence: string;
  recommendation: string;
  dosageModification?: string;
}

export interface LifestyleFactor {
  factor: string;
  impact: string;
  recommendation: string;
}

export interface StackSynergy {
  withIngredient: string;
  effect: string;
  mechanism: string;
  recommendation: string;
  type: 'positive' | 'negative';
}

export interface Recommendation {
  type: 'form' | 'dosage' | 'timing' | 'combination';
  priority: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  impact: string;
}

export interface Warning {
  severity: 'critical' | 'high' | 'medium' | 'low';
  type: 'safety' | 'contraindication' | 'interaction';
  message: string;
  recommendation: string;
}