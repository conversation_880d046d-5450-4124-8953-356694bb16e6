{"extends": "./tsconfig.json", "compilerOptions": {"noEmit": false, "skipLibCheck": true, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitAny": false, "strictNullChecks": false, "strict": false, "allowJs": true, "noImplicitReturns": false, "noImplicitThis": false, "exactOptionalPropertyTypes": false, "noPropertyAccessFromIndexSignature": false}, "exclude": ["**/*.test.ts", "**/*.test.tsx", "**/__tests__/**", "src/tests/**", "src/utils/__tests__/**", "src/services/supabase/functions/**", "**/node_modules/**"]}